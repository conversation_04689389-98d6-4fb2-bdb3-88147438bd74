from dotenv import load_dotenv
import os
import pandas as pd
from llama_index.experimental.query_engine import PandasQueryEngine
from llama_index.llms.openai import OpenAI
from pydantic import BaseModel
from typing import List

load_dotenv()

# Configure pandas to avoid matplotlib plotting issues
pd.options.plotting.backend = "matplotlib"
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# ----------------------------
# Patch safe_eval to fix deprecated .dt.week usage in llama_index
# ----------------------------
from llama_index.experimental import exec_utils

_original_safe_eval = exec_utils.safe_eval

def patched_safe_eval(__source, __globals=None, __locals=None):
    fixed_source = __source.replace(".dt.week", ".dt.isocalendar().week")
    return _original_safe_eval(fixed_source, __globals, __locals)

exec_utils.safe_eval = patched_safe_eval

# ----------------------------
# Global Configuration
# ----------------------------
def get_global_chart_colors():
    """
    Get global chart colors from smart dashboard configuration
    Returns the same color palette used in frontend for consistency
    """
    return [
        '#ffb366', '#ffc999', '#ffab66', '#ffd6b3', '#ff9d4d',
        '#ffe0cc', '#ffb84d', '#fff2e6', '#ffa64d', '#fff5f0',
        '#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f'
    ]

def get_semantic_colors():
    """
    Get semantic color mappings for different chart types and data meanings
    Uses global colors but assigns them semantic meaning
    """
    global_colors = get_global_chart_colors()
    return {
        # Movement types
        'inward': global_colors[4],      # '#ff9d4d' - Orange for positive/inward
        'outward': global_colors[12],    # '#e15759' - Red for negative/outward
        'neutral': global_colors[10],    # '#4e79a7' - Blue for neutral
        'positive': global_colors[14],   # '#59a14f' - Green for positive metrics
        'negative': global_colors[12],   # '#e15759' - Red for negative metrics
        'warning': global_colors[11],    # '#f28e2c' - Orange for warnings

        # Chart type defaults
        'primary': global_colors[0],     # '#ffb366' - Primary brand color
        'secondary': global_colors[1],   # '#ffc999' - Secondary brand color
        'accent': global_colors[2],      # '#ffab66' - Accent color

        # Specific use cases
        'purchase': global_colors[0],    # '#ffb366' - Purchase related
        'inventory': global_colors[13],  # '#76b7b2' - Inventory related
        'sales': global_colors[14],      # '#59a14f' - Sales related
        'spoilage': global_colors[12],   # '#e15759' - Loss/spoilage
        'transfer': global_colors[10],   # '#4e79a7' - Transfers

        # UI colors
        'border_light': '#ffffff',       # White borders
        'border_dark': '#333333',        # Dark borders
        'grid': '#e9ecef',              # Grid lines
    }

# ----------------------------
# Chart.js Data Models
# ----------------------------
class ChartDataset(BaseModel):
    label: str
    data: List[float]
    backgroundColor: List[str]
    borderColor: List[str]

class ChartData(BaseModel):
    labels: List[str]
    datasets: List[ChartDataset]

class Chart(BaseModel):
    id: str
    title: str
    type: str
    data: ChartData

class SummaryItem(BaseModel):
    icon: str
    value: str
    label: str
    data_type: str

class DashboardResponse(BaseModel):
    charts: List[Chart]
    summary_items: List[SummaryItem]

# ----------------------------
# Main Dashboard Function
# ----------------------------
def generate_purchase_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate Purchase Department dashboard with fixed charts and summary cards
    without using LLM - direct DataFrame analysis
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Purchase Records",
                        "data_type": "text"
                    }
                ]
            }

        # 1. Total Purchase Cost (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns:
            total_cost = df['Total(incl.tax,etc)'].sum()
            summary_items.append({
                "icon": "currency_rupee",
                "value": f"₹{total_cost:,.2f}",
                "label": "Total Purchase Cost",
                "data_type": "currency"
            })

        # 2. Total Items Purchased (Summary Item)
        if 'Received Qty' in df.columns:
            total_items = df['Received Qty'].sum()
            summary_items.append({
                "icon": "inventory",
                "value": f"{total_items:,.0f}",
                "label": "Total Items Received",
                "data_type": "number"
            })

        # 3. Average Cost per Item (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            avg_cost = total_cost / total_items if total_items > 0 else 0
            summary_items.append({
                "icon": "calculate",
                "value": f"₹{avg_cost:.2f}",
                "label": "Average Cost per Item",
                "data_type": "currency"
            })

        # 4. Purchase Order Accuracy (Summary Item)
        if 'Order Qty' in df.columns and 'Received Qty' in df.columns:
            total_ordered = df['Order Qty'].sum()
            total_received = df['Received Qty'].sum()
            accuracy = (total_received / total_ordered * 100) if total_ordered > 0 else 0
            summary_items.append({
                "icon": "check_circle",
                "value": f"{accuracy:.1f}%",
                "label": "Purchase Order Accuracy",
                "data_type": "percentage"
            })

        # 5. Purchase Cost by Location (Bar Chart)
        if 'Location' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            location_costs = df.groupby('Location')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not location_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_location",
                    "title": "Purchase Cost by Location",
                    "type": "bar",
                    "data": {
                        "labels": location_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": location_costs.values.tolist(),
                            "backgroundColor": colors[:len(location_costs)],
                            "borderColor": colors[:len(location_costs)]
                        }]
                    }
                })

        # 6. Purchase Cost by Vendor (Bar Chart)
        if 'Vendor Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            vendor_costs = df.groupby('Vendor Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(10)
            if not vendor_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_vendor",
                    "title": "Top 10 Vendors by Purchase Cost",
                    "type": "bar",
                    "data": {
                        "labels": vendor_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": vendor_costs.values.tolist(),
                            "backgroundColor": colors[:len(vendor_costs)],
                            "borderColor": colors[:len(vendor_costs)]
                        }]
                    }
                })

        # 7. Purchase by Category (Doughnut Chart)
        if 'Category' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            category_costs = df.groupby('Category')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not category_costs.empty:
                charts.append({
                    "id": "purchase_by_category",
                    "title": "Purchase Distribution by Category",
                    "type": "doughnut",
                    "data": {
                        "labels": category_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": category_costs.values.tolist(),
                            "backgroundColor": colors[:len(category_costs)],
                            "borderColor": colors[:len(category_costs)]
                        }]
                    }
                })

        # 8. Cost Trends Over Time (Line Chart)
        # Look for date columns - GRN has specific date column names
        date_columns = []
        for col in df.columns:
            if any(date_word in col.lower() for date_word in ['date', 'entry']):
                date_columns.append(col)

        if date_columns and 'Total(incl.tax,etc)' in df.columns:
            date_col = date_columns[0]  # Use first date column found
            try:
                df_copy = df.copy()
                # Handle different date formats
                if df_copy[date_col].dtype == 'object':
                    df_copy[date_col] = pd.to_datetime(df_copy[date_col], errors='coerce')
                daily_costs = df_copy.groupby(df_copy[date_col].dt.date)['Total(incl.tax,etc)'].sum().sort_index()
                if len(daily_costs) > 1:
                    charts.append({
                        "id": "cost_trends",
                        "title": "Purchase Cost Trends Over Time",
                        "type": "line",
                        "data": {
                            "labels": [str(date) for date in daily_costs.index],
                            "datasets": [{
                                "label": "Daily Purchase Cost (₹)",
                                "data": daily_costs.values.tolist(),
                                "backgroundColor": [semantic_colors['purchase']],
                                "borderColor": [semantic_colors['purchase']]
                            }]
                        }
                    })
            except Exception as e:
                print(f"Error creating cost trends chart: {e}")
                pass  # Skip if date parsing fails

        # 9. Top 15 Purchase Items (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            item_costs = df.groupby('Item Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(15)
            if not item_costs.empty:
                charts.append({
                    "id": "top_purchase_items",
                    "title": "Top 15 Purchase Items by Cost",
                    "type": "bar",
                    "data": {
                        "labels": item_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": item_costs.values.tolist(),
                            "backgroundColor": colors[:len(item_costs)],
                            "borderColor": colors[:len(item_costs)]
                        }]
                    }
                })

        # 10. Cost per Item Analysis (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            df_filtered = df[df['Received Qty'] > 0].copy()
            df_filtered['Cost_per_Item'] = df_filtered['Total(incl.tax,etc)'] / df_filtered['Received Qty']
            cost_per_item = df_filtered.groupby('Item Name')['Cost_per_Item'].mean().sort_values(ascending=False).head(10)
            if not cost_per_item.empty:
                charts.append({
                    "id": "cost_per_item",
                    "title": "Top 10 Items by Average Cost per Unit",
                    "type": "bar",
                    "data": {
                        "labels": cost_per_item.index.tolist(),
                        "datasets": [{
                            "label": "Cost per Unit (₹)",
                            "data": cost_per_item.values.tolist(),
                            "backgroundColor": colors[:len(cost_per_item)],
                            "borderColor": colors[:len(cost_per_item)]
                        }]
                    }
                })

        # 11. Total Vendors Count (Summary Item)
        if 'Vendor Name' in df.columns:
            unique_vendors = df['Vendor Name'].nunique()
            summary_items.append({
                "icon": "business",
                "value": f"{unique_vendors}",
                "label": "Active Vendors",
                "data_type": "number"
            })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating purchase dashboard: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def generate_inventory_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate comprehensive Inventory Management dashboard based on store_variance DataFrame
    Focused on essential business insights with non-redundant, high-value charts
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Inventory Records",
                        "data_type": "text"
                    }
                ]
            }

        # Calculate derived metrics for period analysis
        df_calc = df.copy()

        # Fix column name inconsistency and calculate period movements
        df_calc['Period_Net_Inward'] = (
            df_calc.get('Purchase Amount', 0) +
            df_calc.get('Return To Store In Amount', 0) +
            df_calc.get('Ibt In Amount', 0)
        )

        df_calc['Period_Net_Outward'] = (
            df_calc.get('Return Amount', 0) +  # Fixed column name
            df_calc.get('Indent Amount', 0) +
            df_calc.get('Ibt Out Amount', 0) +
            df_calc.get('Spoilage Amount', 0)
        )

        df_calc['Period_Net_Movement'] = df_calc['Period_Net_Inward'] - df_calc['Period_Net_Outward']

        # Turnover calculation (more robust)
        df_calc['Avg_Inventory'] = (df_calc.get('Opening Amount', 0) + df_calc.get('Closing Amount', 0)) / 2
        df_calc['Period_Turnover'] = df_calc.apply(
            lambda row: row['Period_Net_Outward'] / row['Avg_Inventory'] if row['Avg_Inventory'] > 0 else 0, axis=1
        )

        # Growth rate calculation
        df_calc['Period_Growth_Rate'] = df_calc.apply(
            lambda row: ((row.get('Closing Amount', 0) - row.get('Opening Amount', 0)) /
                        row.get('Opening Amount', 1)) * 100 if row.get('Opening Amount', 0) > 0 else 0, axis=1
        )

        # ABC Classification based on closing value
        df_calc = df_calc.sort_values('Closing Amount', ascending=False)
        total_value = df_calc['Closing Amount'].sum()
        df_calc['Cumulative_Percentage'] = (df_calc['Closing Amount'].cumsum() / total_value) * 100
        df_calc['ABC_Class'] = df_calc['Cumulative_Percentage'].apply(
            lambda x: 'A' if x <= 70 else ('B' if x <= 90 else 'C')
        )

        # === SUMMARY CARDS (ESSENTIAL KPIs) ===

        # 1. Financial Performance - Convert to native Python types
        total_opening = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
        total_closing = float(df_calc.get('Closing Amount', pd.Series([0])).sum())
        net_growth = float(((total_closing - total_opening) / total_opening) * 100) if total_opening > 0 else 0.0

        summary_items.extend([
            {
                "icon": "trending_up",
                "value": f"₹{total_opening:,.0f}",
                "label": "Opening Value",
                "data_type": "currency"
            },
            {
                "icon": "account_balance_wallet",
                "value": f"₹{total_closing:,.0f}",
                "label": "Closing Value",
                "data_type": "currency"
            },
            {
                "icon": "percent" if net_growth >= 0 else "trending_down",
                "value": f"{net_growth:+.1f}%",
                "label": "Net Growth",
                "data_type": "percentage"
            }
        ])

        # 2. Movement Analysis - Convert to native Python types
        total_purchases = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
        total_outward = float(df_calc['Period_Net_Outward'].sum())
        avg_turnover = float(df_calc['Period_Turnover'].mean()) if not df_calc['Period_Turnover'].empty else 0.0

        summary_items.extend([
            {
                "icon": "shopping_cart",
                "value": f"₹{total_purchases:,.0f}",
                "label": "Total Purchases",
                "data_type": "currency"
            },
            {
                "icon": "trending_up",
                "value": f"₹{total_outward:,.0f}",
                "label": "Total Consumption",
                "data_type": "currency"
            },
            {
                "icon": "sync",
                "value": f"{avg_turnover:.2f}",
                "label": "Avg Turnover Rate",
                "data_type": "number"
            }
        ])

        # 3. Operational Efficiency - Convert to native Python types
        active_skus = int(len(df_calc[df_calc['Period_Net_Outward'] > 0]))
        total_skus = int(len(df_calc))
        total_spoilage = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
        spoilage_rate = float((total_spoilage / total_outward * 100)) if total_outward > 0 else 0.0

        summary_items.extend([
            {
                "icon": "inventory",
                "value": f"{active_skus}/{total_skus}",
                "label": "Active SKUs",
                "data_type": "text"
            },
            {
                "icon": "warning",
                "value": f"₹{total_spoilage:,.0f}",
                "label": "Total Spoilage",
                "data_type": "currency"
            },
            {
                "icon": "trending_down",
                "value": f"{spoilage_rate:.1f}%",
                "label": "Spoilage Rate",
                "data_type": "percentage"
            }
        ])

        # === ESSENTIAL CHARTS (NON-REDUNDANT, HIGH-VALUE) ===

        # 1. Inventory Flow Analysis (Waterfall Chart) - CRITICAL for understanding inventory movement
        if all(col in df.columns for col in ['Opening Amount', 'Purchase Amount', 'Indent Amount', 'Spoilage Amount', 'Closing Amount']):
            flow_data = {
                'Opening Stock': float(df_calc.get('Opening Amount', pd.Series([0])).sum()),
                'Purchases': float(df_calc.get('Purchase Amount', pd.Series([0])).sum()),
                'IBT In': float(df_calc.get('Ibt In Amount', pd.Series([0])).sum()),
                'Returns In': float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum()),
                'Consumption': float(-df_calc.get('Indent Amount', pd.Series([0])).sum()),
                'IBT Out': float(-df_calc.get('Ibt Out Amount', pd.Series([0])).sum()),
                'Spoilage': float(-df_calc.get('Spoilage Amount', pd.Series([0])).sum()),
                'Closing Stock': float(df_calc.get('Closing Amount', pd.Series([0])).sum())
            }

            # Create waterfall effect data - ensure all values are JSON-serializable
            waterfall_labels = list(flow_data.keys())
            waterfall_values = [float(val) for val in flow_data.values()]

            # Calculate cumulative for waterfall effect
            cumulative = [waterfall_values[0]]  # Start with opening
            for i in range(1, len(waterfall_values) - 1):
                cumulative.append(cumulative[-1] + waterfall_values[i])
            cumulative.append(waterfall_values[-1])  # End with closing

            charts.append({
                "id": "inventory_flow_waterfall",
                "title": "Period Inventory Flow Analysis",
                "type": "bar",
                "data": {
                    "labels": waterfall_labels,
                    "datasets": [{
                        "label": "Inventory Flow (₹)",
                        "data": waterfall_values,
                        "backgroundColor": [
                            semantic_colors['primary'],      # Opening
                            semantic_colors['positive'],     # Purchases
                            semantic_colors['positive'],     # IBT In
                            semantic_colors['positive'],     # Returns In
                            semantic_colors['negative'],     # Consumption
                            semantic_colors['warning'],      # IBT Out
                            semantic_colors['negative'],     # Spoilage
                            semantic_colors['primary']       # Closing
                        ],
                        "borderColor": semantic_colors['border_light'],
                        "borderWidth": 1,
                        "borderRadius": 4
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "plugins": {
                        "legend": {"display": False},
                        "tooltip": {
                            "callbacks": {
                                "label": "function(context) { return context.label + ': ₹' + Math.abs(context.parsed.y).toLocaleString(); }"
                            }
                        }
                    },
                    "scales": {
                        "x": {"grid": {"display": False}},
                        "y": {
                            "beginAtZero": True,
                            "grid": {"color": semantic_colors['grid']},
                            "ticks": {
                                "callback": "function(value) { return '₹' + (Math.abs(value)/1000).toFixed(0) + 'K'; }"
                            }
                        }
                    }
                }
            })

        # 2. ABC Analysis Distribution (Critical for inventory classification)
        if 'ABC_Class' in df_calc.columns:
            abc_analysis = df_calc.groupby('ABC_Class').agg({
                'Closing Amount': 'sum',
                'Item Code': 'count'
            }).reset_index()
            abc_analysis.columns = ['ABC_Class', 'Total_Value', 'Item_Count']

            if not abc_analysis.empty:
                abc_colors = {
                    'A': semantic_colors['positive'],    # Green for high-value
                    'B': semantic_colors['warning'],     # Orange for medium-value
                    'C': semantic_colors['negative']     # Red for low-value
                }

                charts.append({
                    "id": "abc_analysis_distribution",
                    "title": "ABC Analysis - Inventory Classification",
                    "type": "doughnut",
                    "data": {
                        "labels": [f"Class {cls} ({row['Item_Count']} items)" for cls, row in abc_analysis.set_index('ABC_Class').iterrows()],
                        "datasets": [{
                            "label": "Value (₹)",
                            "data": [float(val) for val in abc_analysis['Total_Value'].round(2).tolist()],
                            "backgroundColor": [abc_colors[cls] for cls in abc_analysis['ABC_Class']],
                            "borderColor": semantic_colors['border_light'],
                            "borderWidth": 2,
                            "hoverBorderWidth": 3
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "cutout": '50%',
                        "plugins": {
                            "legend": {
                                "position": "right",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { var total = context.dataset.data.reduce((a, b) => a + b, 0); var percentage = ((context.parsed / total) * 100).toFixed(1); return context.label + ': ₹' + context.parsed.toLocaleString() + ' (' + percentage + '%)'; }"
                                }
                            }
                        }
                    }
                })

        # 3. Category Performance Matrix (Location vs Category Heatmap-style)
        if 'Location' in df.columns and 'Category' in df.columns:
            # Create performance matrix
            performance_matrix = df_calc.groupby(['Location', 'Category']).agg({
                'Closing Amount': 'sum',
                'Period_Turnover': 'mean',
                'Period_Growth_Rate': 'mean'
            }).reset_index()

            if not performance_matrix.empty:
                # Get top categories by total value for better visualization
                top_categories = df_calc.groupby('Category')['Closing Amount'].sum().nlargest(6).index.tolist()
                matrix_filtered = performance_matrix[performance_matrix['Category'].isin(top_categories)]

                # Pivot for better structure
                pivot_data = matrix_filtered.pivot(index='Location', columns='Category', values='Closing Amount').fillna(0)

                if not pivot_data.empty:
                    locations = pivot_data.index.tolist()
                    categories = pivot_data.columns.tolist()

                    # Create datasets for each location
                    datasets = []
                    for i, location in enumerate(locations):
                        datasets.append({
                            "label": location,
                            "data": [float(val) for val in pivot_data.loc[location].round(2).tolist()],
                            "backgroundColor": colors[i % len(colors)],
                            "borderColor": semantic_colors['border_light'],
                            "borderWidth": 1,
                            "borderRadius": 4
                        })

                    charts.append({
                        "id": "location_category_performance",
                        "title": "Location vs Category Performance Matrix",
                        "type": "bar",
                        "data": {
                            "labels": categories,
                            "datasets": datasets
                        },
                        "options": {
                            "responsive": True,
                            "maintainAspectRatio": False,
                            "plugins": {
                                "legend": {
                                    "position": "top",
                                    "labels": {
                                        "usePointStyle": True,
                                        "padding": 12,
                                        "font": {"size": 10}
                                    }
                                },
                                "tooltip": {
                                    "callbacks": {
                                        "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }"
                                    }
                                }
                            },
                            "scales": {
                                "x": {
                                    "grid": {"display": False},
                                    "ticks": {"font": {"size": 9}}
                                },
                                "y": {
                                    "beginAtZero": True,
                                    "grid": {"color": semantic_colors['grid']},
                                    "ticks": {
                                        "font": {"size": 10},
                                        "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                    }
                                }
                            }
                        }
                    })

        # 4. Turnover Rate Analysis by Category (Critical operational metric)
        if 'Category' in df.columns and 'Period_Turnover' in df_calc.columns:
            turnover_by_category = df_calc.groupby('Category').agg({
                'Period_Turnover': 'mean',
                'Closing Amount': 'sum'
            }).sort_values('Period_Turnover', ascending=False)

            if not turnover_by_category.empty:
                # Take top 8 categories for better visualization
                top_turnover_categories = turnover_by_category.head(8)
                categories = top_turnover_categories.index.tolist()
                turnover_rates = [float(val) for val in top_turnover_categories['Period_Turnover'].round(3).tolist()]

                # Color code based on turnover performance
                turnover_colors = []
                for rate in turnover_rates:
                    if rate > 2.0:
                        turnover_colors.append(semantic_colors['positive'])    # High turnover - Good
                    elif rate > 1.0:
                        turnover_colors.append(semantic_colors['warning'])     # Medium turnover
                    else:
                        turnover_colors.append(semantic_colors['negative'])    # Low turnover - Attention needed

                charts.append({
                    "id": "turnover_rate_by_category",
                    "title": "Inventory Turnover Rate by Category",
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [{
                            "label": "Turnover Rate",
                            "data": turnover_rates,
                            "backgroundColor": turnover_colors,
                            "borderColor": semantic_colors['border_light'],
                            "borderWidth": 1,
                            "borderRadius": 6,
                            "borderSkipped": False
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { return 'Turnover Rate: ' + context.parsed.y.toFixed(2) + 'x'; }",
                                    "afterLabel": "function(context) { return context.parsed.y > 2 ? 'Excellent' : context.parsed.y > 1 ? 'Good' : 'Needs Attention'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + 'x'; }"
                                }
                            }
                        }
                    }
                })

        # 5. Top Performing Items (Value + Turnover Combined Analysis)
        if 'Item Name' in df.columns and 'Period_Turnover' in df_calc.columns:
            # Get items with both high value and good turnover
            top_performers = df_calc.nlargest(15, 'Closing Amount')[['Item Name', 'Closing Amount', 'Period_Turnover']]

            if not top_performers.empty:
                item_names = [name[:20] + "..." if len(name) > 20 else name for name in top_performers['Item Name'].tolist()]

                charts.append({
                    "id": "top_performing_items",
                    "title": "Top 15 High-Value Items with Turnover Analysis",
                    "type": "scatter",
                    "data": {
                        "datasets": [{
                            "label": "Items Performance",
                            "data": [
                                {
                                    "x": float(row['Closing Amount']),
                                    "y": float(row['Period_Turnover']),
                                    "label": name
                                } for name, (_, row) in zip(item_names, top_performers.iterrows())
                            ],
                            "backgroundColor": colors[0],
                            "borderColor": semantic_colors['primary'],
                            "borderWidth": 2,
                            "pointRadius": 8,
                            "pointHoverRadius": 12
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return context[0].raw.label; }",
                                    "label": "function(context) { return 'Value: ₹' + context.parsed.x.toLocaleString() + ', Turnover: ' + context.parsed.y.toFixed(2) + 'x'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "type": "linear",
                                "position": "bottom",
                                "title": {
                                    "display": True,
                                    "text": "Inventory Value (₹)"
                                },
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            },
                            "y": {
                                "title": {
                                    "display": True,
                                    "text": "Turnover Rate"
                                },
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "callback": "function(value) { return value.toFixed(1) + 'x'; }"
                                }
                            }
                        }
                    }
                })

        # 6. Loss & Risk Analysis (Combined Spoilage + Low Turnover)
        if 'Spoilage Amount' in df.columns and 'Category' in df.columns:
            # Combine spoilage analysis with low turnover risk
            risk_analysis = df_calc.groupby('Category').agg({
                'Spoilage Amount': 'sum',
                'Period_Turnover': 'mean',
                'Closing Amount': 'sum'
            }).fillna(0)

            # Calculate risk score (high spoilage + low turnover = high risk)
            risk_analysis['Risk_Score'] = (
                (risk_analysis['Spoilage Amount'] / risk_analysis['Closing Amount']) * 100 +
                (1 / (risk_analysis['Period_Turnover'] + 0.1)) * 10  # Inverse of turnover
            )

            # Get top risk categories
            top_risk_categories = risk_analysis.nlargest(6, 'Risk_Score')

            if not top_risk_categories.empty:
                categories = top_risk_categories.index.tolist()
                spoilage_values = top_risk_categories['Spoilage Amount'].round(2).tolist()
                risk_scores = top_risk_categories['Risk_Score'].round(1).tolist()

                # Color code based on risk level
                risk_colors = []
                for score in risk_scores:
                    if score > 15:
                        risk_colors.append(semantic_colors['negative'])    # High risk
                    elif score > 8:
                        risk_colors.append(semantic_colors['warning'])     # Medium risk
                    else:
                        risk_colors.append(semantic_colors['positive'])    # Low risk

                charts.append({
                    "id": "loss_risk_analysis",
                    "title": "Loss & Risk Analysis by Category",
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "Spoilage Loss (₹)",
                                "data": spoilage_values,
                                "backgroundColor": risk_colors,
                                "borderColor": semantic_colors['border_light'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "yAxisID": 'y'
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { return 'Spoilage: ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterLabel": "function(context) { var riskScores = " + str(risk_scores) + "; return 'Risk Score: ' + riskScores[context.dataIndex]; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating inventory dashboard: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def smart_ask_dashboard(df: pd.DataFrame, user_query: str = "", use_default_charts: bool = True) -> dict:
    openai_api_key = os.getenv("OPENAI_API_KEY")
    llm = OpenAI(api_key=openai_api_key, model="gpt-4")

    # Get global chart colors for consistency
    global_colors = get_global_chart_colors()
    colors_str = str(global_colors)

    columns = list(df.columns)
    
    if use_default_charts or not user_query:
        analysis_prompt = f"""
            You are a data analyst generating a dashboard for an Indian restaurant business.

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            
            - **Purchase Overview Section**: A dedicated section showcasing purchase metrics.
            - **Total Purchase Cost**: Bar chart displaying the total cost of purchases.
            - **Purchase Cost by Location**, Supplier: Grouped bar chart or stacked bar chart illustrating spending patterns.
            - **Cost Trends**: Line chart or area chart depicting cost trends over time.
            - **Overall Purchase Value Comparison**: Bar chart or bullet graph for holistic spending analysis.
            - **Cost per Item**: Line chart or bar chart showing the average cost per item.
            - **Purchase Order Accuracy**: Area chart or line chart indicating the accuracy of purchase orders.
            - **Purchase by Category**: Stacked bar chart or table showcasing purchase distribution by categories.
            - **Top 15 Purchase Items**: Table or bar chart displaying the top 15 purchased items.

            Rules:
            - Use only actual data values.
            - For date-wise insights, use columns containing 'Based on'.
            - No assumptions or fabrication.

            Your task:
            - Extract factual business metrics.
            - Identify trends, totals, comparisons.
            - Keep insights concise.
        """
    else:
        analysis_prompt = f"""
            You are a data analyst answering this business question for an Indian restaurant:

            "{user_query}"

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            Rules:
            - Use only actual data.
            - Use only date columns containing 'Based on'.
            - No guessing or fabrication.
            - Be concise with numeric summaries or patterns.
        """
    
    query_engine = PandasQueryEngine(df=df, llm=llm, verbose=False, synthesize_response=True)
    analysis_response = query_engine.query(analysis_prompt)
    analysis_text = str(analysis_response).strip()

    print("Analysis text:", analysis_text)

    formatting_prompt = f"""
        Convert the following analysis text into a single valid JSON matching this schema:

        {{
        "charts": [
            {{
            "id": "string",
            "title": "string",
            "type": "string",
            "data": {{
                "labels": ["string"],
                "datasets": [
                {{
                    "label": "string",
                    "data": [float],
                    "backgroundColor": ["string"],
                    "borderColor": ["string"]
                }}
                ]
            }}
            }}
        ],
        "summary_items": [
            {{
            "icon": "string",
            "value": "string",
            "label": "string",
            "data_type": "string"
            }}
        ]
        }}

        Use only facts from the text below — no extra info or calculations.
        Match label and data array lengths exactly.
        Use these colors in order for charts: {colors_str}
        Output ONLY JSON — no explanations or text.

        IMPORTANT:
        - Summary items should be concise top-level business KPIs.
        - Do NOT repeat any data already shown in charts within summary items.

        Analysis text:
        \"\"\"
        {analysis_text}
        \"\"\"
    """

    json_response = llm.complete(prompt=formatting_prompt).text.strip()
    dashboard = DashboardResponse.parse_raw(json_response)

    return {
        "success": True,
        "charts": dashboard.charts,
        "summary_items": dashboard.summary_items
    }
