from dotenv import load_dotenv
import os
import pandas as pd
from llama_index.experimental.query_engine import PandasQueryEngine
from llama_index.llms.openai import OpenAI
from pydantic import BaseModel
from typing import List

load_dotenv()

# Configure pandas to avoid matplotlib plotting issues
pd.options.plotting.backend = "matplotlib"
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# ----------------------------
# Patch safe_eval to fix deprecated .dt.week usage in llama_index
# ----------------------------
from llama_index.experimental import exec_utils

_original_safe_eval = exec_utils.safe_eval

def patched_safe_eval(__source, __globals=None, __locals=None):
    fixed_source = __source.replace(".dt.week", ".dt.isocalendar().week")
    return _original_safe_eval(fixed_source, __globals, __locals)

exec_utils.safe_eval = patched_safe_eval

# ----------------------------
# Global Configuration
# ----------------------------
def get_global_chart_colors():
    """
    Get global chart colors from smart dashboard configuration
    Returns the same color palette used in frontend for consistency
    """
    return [
        '#ffb366', '#ffc999', '#ffab66', '#ffd6b3', '#ff9d4d',
        '#ffe0cc', '#ffb84d', '#fff2e6', '#ffa64d', '#fff5f0',
        '#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f'
    ]

def get_semantic_colors():
    """
    Get semantic color mappings for different chart types and data meanings
    Uses global colors but assigns them semantic meaning
    """
    global_colors = get_global_chart_colors()
    return {
        # Movement types
        'inward': global_colors[4],      # '#ff9d4d' - Orange for positive/inward
        'outward': global_colors[12],    # '#e15759' - Red for negative/outward
        'neutral': global_colors[10],    # '#4e79a7' - Blue for neutral
        'positive': global_colors[14],   # '#59a14f' - Green for positive metrics
        'negative': global_colors[12],   # '#e15759' - Red for negative metrics
        'warning': global_colors[11],    # '#f28e2c' - Orange for warnings

        # Chart type defaults
        'primary': global_colors[0],     # '#ffb366' - Primary brand color
        'secondary': global_colors[1],   # '#ffc999' - Secondary brand color
        'accent': global_colors[2],      # '#ffab66' - Accent color

        # Specific use cases
        'purchase': global_colors[0],    # '#ffb366' - Purchase related
        'inventory': global_colors[13],  # '#76b7b2' - Inventory related
        'sales': global_colors[14],      # '#59a14f' - Sales related
        'spoilage': global_colors[12],   # '#e15759' - Loss/spoilage
        'transfer': global_colors[10],   # '#4e79a7' - Transfers

        # UI colors
        'border_light': '#ffffff',       # White borders
        'border_dark': '#333333',        # Dark borders
        'grid': '#e9ecef',              # Grid lines
    }

# ----------------------------
# Chart.js Data Models
# ----------------------------
class ChartDataset(BaseModel):
    label: str
    data: List[float]
    backgroundColor: List[str]
    borderColor: List[str]

class ChartData(BaseModel):
    labels: List[str]
    datasets: List[ChartDataset]

class Chart(BaseModel):
    id: str
    title: str
    type: str
    data: ChartData

class SummaryItem(BaseModel):
    icon: str
    value: str
    label: str
    data_type: str

class DashboardResponse(BaseModel):
    charts: List[Chart]
    summary_items: List[SummaryItem]

# ----------------------------
# Main Dashboard Function
# ----------------------------
def generate_purchase_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate Purchase Department dashboard with fixed charts and summary cards
    without using LLM - direct DataFrame analysis
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Purchase Records",
                        "data_type": "text"
                    }
                ]
            }

        # 1. Total Purchase Cost (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns:
            total_cost = df['Total(incl.tax,etc)'].sum()
            summary_items.append({
                "icon": "currency_rupee",
                "value": f"₹{total_cost:,.2f}",
                "label": "Total Purchase Cost",
                "data_type": "currency"
            })

        # 2. Total Items Purchased (Summary Item)
        if 'Received Qty' in df.columns:
            total_items = df['Received Qty'].sum()
            summary_items.append({
                "icon": "inventory",
                "value": f"{total_items:,.0f}",
                "label": "Total Items Received",
                "data_type": "number"
            })

        # 3. Average Cost per Item (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            avg_cost = total_cost / total_items if total_items > 0 else 0
            summary_items.append({
                "icon": "calculate",
                "value": f"₹{avg_cost:.2f}",
                "label": "Average Cost per Item",
                "data_type": "currency"
            })

        # 4. Purchase Order Accuracy (Summary Item)
        if 'Order Qty' in df.columns and 'Received Qty' in df.columns:
            total_ordered = df['Order Qty'].sum()
            total_received = df['Received Qty'].sum()
            accuracy = (total_received / total_ordered * 100) if total_ordered > 0 else 0
            summary_items.append({
                "icon": "check_circle",
                "value": f"{accuracy:.1f}%",
                "label": "Purchase Order Accuracy",
                "data_type": "percentage"
            })

        # 5. Purchase Cost by Location (Bar Chart)
        if 'Location' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            location_costs = df.groupby('Location')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not location_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_location",
                    "title": "Purchase Cost by Location",
                    "type": "bar",
                    "data": {
                        "labels": location_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": location_costs.values.tolist(),
                            "backgroundColor": colors[:len(location_costs)],
                            "borderColor": colors[:len(location_costs)]
                        }]
                    }
                })

        # 6. Purchase Cost by Vendor (Bar Chart)
        if 'Vendor Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            vendor_costs = df.groupby('Vendor Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(10)
            if not vendor_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_vendor",
                    "title": "Top 10 Vendors by Purchase Cost",
                    "type": "bar",
                    "data": {
                        "labels": vendor_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": vendor_costs.values.tolist(),
                            "backgroundColor": colors[:len(vendor_costs)],
                            "borderColor": colors[:len(vendor_costs)]
                        }]
                    }
                })

        # 7. Purchase by Category (Doughnut Chart)
        if 'Category' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            category_costs = df.groupby('Category')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not category_costs.empty:
                charts.append({
                    "id": "purchase_by_category",
                    "title": "Purchase Distribution by Category",
                    "type": "doughnut",
                    "data": {
                        "labels": category_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": category_costs.values.tolist(),
                            "backgroundColor": colors[:len(category_costs)],
                            "borderColor": colors[:len(category_costs)]
                        }]
                    }
                })

        # 8. Cost Trends Over Time (Line Chart)
        # Look for date columns - GRN has specific date column names
        date_columns = []
        for col in df.columns:
            if any(date_word in col.lower() for date_word in ['date', 'entry']):
                date_columns.append(col)

        if date_columns and 'Total(incl.tax,etc)' in df.columns:
            date_col = date_columns[0]  # Use first date column found
            try:
                df_copy = df.copy()
                # Handle different date formats
                if df_copy[date_col].dtype == 'object':
                    df_copy[date_col] = pd.to_datetime(df_copy[date_col], errors='coerce')
                daily_costs = df_copy.groupby(df_copy[date_col].dt.date)['Total(incl.tax,etc)'].sum().sort_index()
                if len(daily_costs) > 1:
                    charts.append({
                        "id": "cost_trends",
                        "title": "Purchase Cost Trends Over Time",
                        "type": "line",
                        "data": {
                            "labels": [str(date) for date in daily_costs.index],
                            "datasets": [{
                                "label": "Daily Purchase Cost (₹)",
                                "data": daily_costs.values.tolist(),
                                "backgroundColor": [semantic_colors['purchase']],
                                "borderColor": [semantic_colors['purchase']]
                            }]
                        }
                    })
            except Exception as e:
                print(f"Error creating cost trends chart: {e}")
                pass  # Skip if date parsing fails

        # 9. Top 15 Purchase Items (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            item_costs = df.groupby('Item Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(15)
            if not item_costs.empty:
                charts.append({
                    "id": "top_purchase_items",
                    "title": "Top 15 Purchase Items by Cost",
                    "type": "bar",
                    "data": {
                        "labels": item_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": item_costs.values.tolist(),
                            "backgroundColor": colors[:len(item_costs)],
                            "borderColor": colors[:len(item_costs)]
                        }]
                    }
                })

        # 10. Cost per Item Analysis (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            df_filtered = df[df['Received Qty'] > 0].copy()
            df_filtered['Cost_per_Item'] = df_filtered['Total(incl.tax,etc)'] / df_filtered['Received Qty']
            cost_per_item = df_filtered.groupby('Item Name')['Cost_per_Item'].mean().sort_values(ascending=False).head(10)
            if not cost_per_item.empty:
                charts.append({
                    "id": "cost_per_item",
                    "title": "Top 10 Items by Average Cost per Unit",
                    "type": "bar",
                    "data": {
                        "labels": cost_per_item.index.tolist(),
                        "datasets": [{
                            "label": "Cost per Unit (₹)",
                            "data": cost_per_item.values.tolist(),
                            "backgroundColor": colors[:len(cost_per_item)],
                            "borderColor": colors[:len(cost_per_item)]
                        }]
                    }
                })

        # 11. Total Vendors Count (Summary Item)
        if 'Vendor Name' in df.columns:
            unique_vendors = df['Vendor Name'].nunique()
            summary_items.append({
                "icon": "business",
                "value": f"{unique_vendors}",
                "label": "Active Vendors",
                "data_type": "number"
            })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating purchase dashboard: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def generate_inventory_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate comprehensive Inventory Management dashboard based on store_variance DataFrame
    Implements detailed KPI structure with financial and operational metrics
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()



        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Inventory Records",
                        "data_type": "text"
                    }
                ]
            }

        # Calculate derived metrics for period analysis
        df_calc = df.copy()

        # Period movement calculations
        df_calc['Period_Net_Inward'] = (
            df_calc.get('Purchase Amount', 0) +
            df_calc.get('Return To Store In Amount', 0) +
            df_calc.get('Ibt In Amount', 0)
        )

        df_calc['Period_Net_Outward'] = (
            df_calc.get('Return-Qty Amount', 0) +
            df_calc.get('Indent Amount', 0) +
            df_calc.get('Ibt Out Amount', 0) +
            df_calc.get('Spoilage Amount', 0)
        )

        df_calc['Period_Net_Movement'] = df_calc['Period_Net_Inward'] - df_calc['Period_Net_Outward']

        # Turnover calculation
        avg_inventory = (df_calc.get('Opening Amount', 0) + df_calc.get('Closing Amount', 0)) / 2
        df_calc['Period_Turnover'] = df_calc['Period_Net_Outward'] / (avg_inventory + 1)  # +1 to avoid division by zero

        # Growth rate calculation
        df_calc['Period_Growth_Rate'] = ((df_calc.get('Closing Amount', 0) - df_calc.get('Opening Amount', 0)) /
                                        (df_calc.get('Opening Amount', 0) + 1)) * 100

        # === SUMMARY CARDS (TOP ROW KPIs) ===

        # 1. Period Inventory Performance
        total_opening = df_calc.get('Opening Amount', pd.Series([0])).sum()
        total_closing = df_calc.get('Closing Amount', pd.Series([0])).sum()
        net_growth = ((total_closing - total_opening) / (total_opening + 1)) * 100 if total_opening > 0 else 0

        summary_items.extend([
            {
                "icon": "trending_up",
                "value": f"₹{total_opening:,.0f}",
                "label": "Opening Value",
                "data_type": "currency"
            },
            {
                "icon": "account_balance_wallet",
                "value": f"₹{total_closing:,.0f}",
                "label": "Closing Value",
                "data_type": "currency"
            },
            {
                "icon": "percent" if net_growth >= 0 else "trending_down",
                "value": f"{net_growth:+.1f}%",
                "label": "Net Growth",
                "data_type": "percentage"
            }
        ])

        # 2. Period Investment Analysis
        total_purchases = df_calc.get('Purchase Amount', pd.Series([0])).sum()
        purchase_roi = ((total_closing - total_opening) / (total_purchases + 1)) * 100 if total_purchases > 0 else 0

        summary_items.extend([
            {
                "icon": "shopping_cart",
                "value": f"₹{total_purchases:,.0f}",
                "label": "Total Purchases",
                "data_type": "currency"
            },
            {
                "icon": "analytics",
                "value": f"{purchase_roi:+.1f}%",
                "label": "Purchase ROI",
                "data_type": "percentage"
            }
        ])

        # 3. Period Movement Summary
        total_inward = df_calc['Period_Net_Inward'].sum()
        total_outward = df_calc['Period_Net_Outward'].sum()
        net_movement = total_inward - total_outward

        summary_items.extend([
            {
                "icon": "arrow_downward",
                "value": f"₹{total_inward:,.0f}",
                "label": "Total Inward",
                "data_type": "currency"
            },
            {
                "icon": "arrow_upward",
                "value": f"₹{total_outward:,.0f}",
                "label": "Total Outward",
                "data_type": "currency"
            },
            {
                "icon": "compare_arrows",
                "value": f"₹{net_movement:,.0f}",
                "label": "Net Movement",
                "data_type": "currency"
            }
        ])

        # 4. Inventory Turnover Rate
        avg_turnover = df_calc['Period_Turnover'].mean()
        summary_items.append({
            "icon": "sync",
            "value": f"{avg_turnover:.2f}",
            "label": "Avg Turnover Rate",
            "data_type": "number"
        })

        # === CHARTS ===

        # 1. Inventory Value by Category (Enhanced Doughnut Chart)
        if 'Category' in df.columns and 'Closing Amount' in df.columns:
            category_values = df.groupby('Category')['Closing Amount'].sum().sort_values(ascending=False)
            if not category_values.empty:
                # Use distinct colors for better category differentiation
                category_colors = colors[:len(category_values)]
                charts.append({
                    "id": "inventory_value_by_category",
                    "title": "Inventory Value Distribution by Category",
                    "type": "doughnut",
                    "data": {
                        "labels": category_values.index.tolist(),
                        "datasets": [{
                            "label": "Value (₹)",
                            "data": category_values.values.round(2).tolist(),
                            "backgroundColor": category_colors,
                            "borderColor": semantic_colors['border_light'],
                            "borderWidth": 2,
                            "hoverBackgroundColor": [color + '80' for color in category_colors],  # Add transparency on hover
                            "hoverBorderColor": semantic_colors['border_dark'],
                            "hoverBorderWidth": 3
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "cutout": '60%',  # Makes it a proper doughnut
                        "plugins": {
                            "legend": {
                                "position": "right",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 12}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { return context.label + ': ₹' + context.parsed.toLocaleString(); }"
                                }
                            }
                        }
                    }
                })

        # 2. Period Movement Analysis (Enhanced Grouped Bar Chart)
        if 'Category' in df.columns:
            movement_by_category = df_calc.groupby('Category').agg({
                'Purchase Amount': 'sum',
                'Indent Amount': 'sum',
                'Spoilage Amount': 'sum',
                'Return-Qty Amount': 'sum'
            }).fillna(0)

            if not movement_by_category.empty:
                categories = movement_by_category.index.tolist()
                # Use semantic colors for different movement types
                movement_colors = {
                    'Purchases': semantic_colors['positive'],    # Green for inward/positive
                    'Indents': semantic_colors['outward'],       # Red for outward/usage
                    'Spoilage': semantic_colors['negative'],     # Red for losses
                    'Returns': semantic_colors['warning']        # Orange for returns
                }

                charts.append({
                    "id": "period_movement_analysis",
                    "title": "Inventory Movement Analysis by Category",
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "Purchases (Inward)",
                                "data": movement_by_category['Purchase Amount'].round(2).tolist(),
                                "backgroundColor": movement_colors['Purchases'],
                                "borderColor": movement_colors['Purchases'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "borderSkipped": False
                            },
                            {
                                "label": "Indents (Outward)",
                                "data": movement_by_category['Indent Amount'].round(2).tolist(),
                                "backgroundColor": movement_colors['Indents'],
                                "borderColor": movement_colors['Indents'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "borderSkipped": False
                            },
                            {
                                "label": "Spoilage (Loss)",
                                "data": movement_by_category['Spoilage Amount'].round(2).tolist(),
                                "backgroundColor": movement_colors['Spoilage'],
                                "borderColor": movement_colors['Spoilage'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "borderSkipped": False
                            },
                            {
                                "label": "Returns",
                                "data": movement_by_category['Return-Qty Amount'].round(2).tolist(),
                                "backgroundColor": movement_colors['Returns'],
                                "borderColor": movement_colors['Returns'],
                                "borderWidth": 1,
                                "borderRadius": 4,
                                "borderSkipped": False
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + value.toLocaleString(); }"
                                }
                            }
                        }
                    }
                })

        # 3. Top 10 Items by Value (Enhanced Horizontal Bar Chart)
        if 'Item Name' in df.columns and 'Closing Amount' in df.columns:
            top_items = df.nlargest(10, 'Closing Amount')[['Item Name', 'Closing Amount', 'Opening Amount']]
            if not top_items.empty:
                item_names = [name[:25] + "..." if len(name) > 25 else name for name in top_items['Item Name'].tolist()]

                # Create gradient colors for top items using global colors
                closing_colors = []
                opening_colors = []
                for i in range(len(item_names)):
                    # Use global colors with decreasing intensity based on ranking
                    closing_colors.append(colors[i % len(colors)])
                    opening_colors.append(colors[(i + 1) % len(colors)])

                charts.append({
                    "id": "top_items_by_value",
                    "title": "Top 10 High-Value Inventory Items",
                    "type": "horizontalBar",
                    "data": {
                        "labels": item_names,
                        "datasets": [
                            {
                                "label": "Current Value (Closing)",
                                "data": top_items['Closing Amount'].round(2).tolist(),
                                "backgroundColor": closing_colors,
                                "borderColor": semantic_colors['inventory'],
                                "borderWidth": 1,
                                "borderRadius": 6,
                                "borderSkipped": False
                            },
                            {
                                "label": "Opening Value",
                                "data": top_items['Opening Amount'].round(2).tolist(),
                                "backgroundColor": opening_colors,
                                "borderColor": semantic_colors['primary'],
                                "borderWidth": 1,
                                "borderRadius": 6,
                                "borderSkipped": False
                            }
                        ]
                    },
                    "options": {
                        "indexAxis": "y",
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.x.toLocaleString(); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "beginAtZero": True,
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            },
                            "y": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 9}}
                            }
                        }
                    }
                })

        # 4. Movement Type Breakdown (Enhanced Stacked Bar Chart)
        if 'Category' in df.columns:
            movement_breakdown = df_calc.groupby('Category').agg({
                'Purchase Amount': 'sum',
                'Ibt In Amount': 'sum',
                'Indent Amount': 'sum',
                'Ibt Out Amount': 'sum',
                'Spoilage Amount': 'sum'
            }).fillna(0)

            if not movement_breakdown.empty:
                categories = movement_breakdown.index.tolist()

                # Define movement type colors with semantic meaning
                movement_type_colors = {
                    'Purchase': semantic_colors['positive'],     # Green - Inward/Positive
                    'IBT In': semantic_colors['transfer'],       # Blue - Transfer In
                    'Indent': semantic_colors['outward'],        # Red - Outward/Usage
                    'IBT Out': semantic_colors['warning'],       # Orange - Transfer Out
                    'Spoilage': semantic_colors['negative']      # Red - Loss
                }

                charts.append({
                    "id": "movement_type_breakdown",
                    "title": "Complete Movement Breakdown by Category",
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "Purchase (Inward)",
                                "data": movement_breakdown['Purchase Amount'].round(2).tolist(),
                                "backgroundColor": movement_type_colors['Purchase'],
                                "borderColor": movement_type_colors['Purchase'],
                                "borderWidth": 0,
                                "stack": 'inward'
                            },
                            {
                                "label": "IBT In (Transfer)",
                                "data": movement_breakdown['Ibt In Amount'].round(2).tolist(),
                                "backgroundColor": movement_type_colors['IBT In'],
                                "borderColor": movement_type_colors['IBT In'],
                                "borderWidth": 0,
                                "stack": 'inward'
                            },
                            {
                                "label": "Indent (Usage)",
                                "data": movement_breakdown['Indent Amount'].round(2).tolist(),
                                "backgroundColor": movement_type_colors['Indent'],
                                "borderColor": movement_type_colors['Indent'],
                                "borderWidth": 0,
                                "stack": 'outward'
                            },
                            {
                                "label": "IBT Out (Transfer)",
                                "data": movement_breakdown['Ibt Out Amount'].round(2).tolist(),
                                "backgroundColor": movement_type_colors['IBT Out'],
                                "borderColor": movement_type_colors['IBT Out'],
                                "borderWidth": 0,
                                "stack": 'outward'
                            },
                            {
                                "label": "Spoilage (Loss)",
                                "data": movement_breakdown['Spoilage Amount'].round(2).tolist(),
                                "backgroundColor": movement_type_colors['Spoilage'],
                                "borderColor": movement_type_colors['Spoilage'],
                                "borderWidth": 0,
                                "stack": 'outward'
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "interaction": {
                            "mode": "index",
                            "intersect": False
                        },
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 12,
                                    "font": {"size": 10}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "footer": "function(tooltipItems) { var sum = 0; tooltipItems.forEach(function(tooltipItem) { sum += tooltipItem.parsed.y; }); return 'Total: ₹' + sum.toLocaleString(); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "stacked": True,
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "stacked": True,
                                "beginAtZero": True,
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 5. Location-wise Performance (Enhanced Multi-metric Chart)
        if 'Location' in df.columns:
            location_performance = df_calc.groupby('Location').agg({
                'Closing Amount': 'sum',
                'Period_Turnover': 'mean',
                'Period_Growth_Rate': 'mean'
            }).fillna(0)

            if not location_performance.empty:
                locations = location_performance.index.tolist()

                # Create gradient colors based on performance using global colors
                performance_colors = []
                for i in range(len(location_performance)):
                    # Use global colors cycling through the palette
                    performance_colors.append(colors[i % len(colors)])

                charts.append({
                    "id": "location_performance",
                    "title": "Location-wise Inventory Performance",
                    "type": "bar",
                    "data": {
                        "labels": locations,
                        "datasets": [
                            {
                                "label": "Inventory Value (₹)",
                                "data": location_performance['Closing Amount'].round(2).tolist(),
                                "backgroundColor": performance_colors,
                                "borderColor": semantic_colors['inventory'],
                                "borderWidth": 2,
                                "borderRadius": 8,
                                "borderSkipped": False
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { return 'Value: ₹' + context.parsed.y.toLocaleString(); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 11}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": semantic_colors['grid']},
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/100000).toFixed(1) + 'L'; }"
                                }
                            }
                        }
                    }
                })

        # 6. Spoilage Analysis (Enhanced Pie Chart with Alert Colors)
        if 'Spoilage Amount' in df.columns and 'Category' in df.columns:
            spoilage_by_category = df_calc.groupby('Category')['Spoilage Amount'].sum()
            spoilage_by_category = spoilage_by_category[spoilage_by_category > 0].sort_values(ascending=False)

            if not spoilage_by_category.empty:
                # Use global colors for spoilage with semantic meaning
                spoilage_colors = [semantic_colors['negative']] * len(spoilage_by_category)
                # If we need more colors, cycle through global colors
                if len(spoilage_by_category) > 1:
                    spoilage_colors = [colors[i % len(colors)] for i in range(len(spoilage_by_category))]

                charts.append({
                    "id": "spoilage_analysis",
                    "title": "Spoilage Loss Distribution by Category",
                    "type": "pie",
                    "data": {
                        "labels": spoilage_by_category.index.tolist(),
                        "datasets": [{
                            "label": "Spoilage Loss (₹)",
                            "data": spoilage_by_category.values.round(2).tolist(),
                            "backgroundColor": spoilage_colors,
                            "borderColor": semantic_colors['border_light'],
                            "borderWidth": 2,
                            "hoverBackgroundColor": [color + '80' for color in spoilage_colors],
                            "hoverBorderColor": semantic_colors['border_dark'],
                            "hoverBorderWidth": 3
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "right",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "label": "function(context) { var total = context.dataset.data.reduce((a, b) => a + b, 0); var percentage = ((context.parsed / total) * 100).toFixed(1); return context.label + ': ₹' + context.parsed.toLocaleString() + ' (' + percentage + '%)'; }"
                                }
                            }
                        }
                    }
                })

        # Additional Summary Items for Operational Metrics

        # 5. Active SKUs in Period
        active_skus = len(df_calc[
            (df_calc.get('Purchase Amount', 0) > 0) |
            (df_calc.get('Indent Amount', 0) > 0) |
            (df_calc.get('Ibt In Amount', 0) > 0) |
            (df_calc.get('Ibt Out Amount', 0) > 0)
        ])
        total_skus = len(df_calc)
        activity_percentage = (active_skus / total_skus * 100) if total_skus > 0 else 0

        summary_items.extend([
            {
                "icon": "inventory",
                "value": f"{active_skus}",
                "label": "Active SKUs",
                "data_type": "number"
            },
            {
                "icon": "percent",
                "value": f"{activity_percentage:.1f}%",
                "label": "SKU Activity Rate",
                "data_type": "percentage"
            }
        ])

        # 6. Loss Analysis
        total_spoilage = df_calc.get('Spoilage Amount', pd.Series([0])).sum()
        spoilage_rate = (total_spoilage / total_outward * 100) if total_outward > 0 else 0
        items_with_spoilage = len(df_calc[df_calc.get('Spoilage Amount', 0) > 0])

        summary_items.extend([
            {
                "icon": "warning",
                "value": f"₹{total_spoilage:,.0f}",
                "label": "Total Spoilage",
                "data_type": "currency"
            },
            {
                "icon": "trending_down",
                "value": f"{spoilage_rate:.1f}%",
                "label": "Spoilage Rate",
                "data_type": "percentage"
            },
            {
                "icon": "error",
                "value": f"{items_with_spoilage}",
                "label": "Items with Spoilage",
                "data_type": "number"
            }
        ])

        # 7. Stock Efficiency
        zero_movement_items = len(df_calc[
            (df_calc.get('Purchase Amount', 0) == 0) &
            (df_calc.get('Indent Amount', 0) == 0) &
            (df_calc.get('Closing Amount', 0) > 0)
        ])

        summary_items.append({
            "icon": "pause_circle",
            "value": f"{zero_movement_items}",
            "label": "Zero Movement Items",
            "data_type": "number"
        })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating inventory dashboard: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def smart_ask_dashboard(df: pd.DataFrame, user_query: str = "", use_default_charts: bool = True) -> dict:
    openai_api_key = os.getenv("OPENAI_API_KEY")
    llm = OpenAI(api_key=openai_api_key, model="gpt-4")

    # Get global chart colors for consistency
    global_colors = get_global_chart_colors()
    colors_str = str(global_colors)

    columns = list(df.columns)
    
    if use_default_charts or not user_query:
        analysis_prompt = f"""
            You are a data analyst generating a dashboard for an Indian restaurant business.

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            
            - **Purchase Overview Section**: A dedicated section showcasing purchase metrics.
            - **Total Purchase Cost**: Bar chart displaying the total cost of purchases.
            - **Purchase Cost by Location**, Supplier: Grouped bar chart or stacked bar chart illustrating spending patterns.
            - **Cost Trends**: Line chart or area chart depicting cost trends over time.
            - **Overall Purchase Value Comparison**: Bar chart or bullet graph for holistic spending analysis.
            - **Cost per Item**: Line chart or bar chart showing the average cost per item.
            - **Purchase Order Accuracy**: Area chart or line chart indicating the accuracy of purchase orders.
            - **Purchase by Category**: Stacked bar chart or table showcasing purchase distribution by categories.
            - **Top 15 Purchase Items**: Table or bar chart displaying the top 15 purchased items.

            Rules:
            - Use only actual data values.
            - For date-wise insights, use columns containing 'Based on'.
            - No assumptions or fabrication.

            Your task:
            - Extract factual business metrics.
            - Identify trends, totals, comparisons.
            - Keep insights concise.
        """
    else:
        analysis_prompt = f"""
            You are a data analyst answering this business question for an Indian restaurant:

            "{user_query}"

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            Rules:
            - Use only actual data.
            - Use only date columns containing 'Based on'.
            - No guessing or fabrication.
            - Be concise with numeric summaries or patterns.
        """
    
    query_engine = PandasQueryEngine(df=df, llm=llm, verbose=False, synthesize_response=True)
    analysis_response = query_engine.query(analysis_prompt)
    analysis_text = str(analysis_response).strip()

    print("Analysis text:", analysis_text)

    formatting_prompt = f"""
        Convert the following analysis text into a single valid JSON matching this schema:

        {{
        "charts": [
            {{
            "id": "string",
            "title": "string",
            "type": "string",
            "data": {{
                "labels": ["string"],
                "datasets": [
                {{
                    "label": "string",
                    "data": [float],
                    "backgroundColor": ["string"],
                    "borderColor": ["string"]
                }}
                ]
            }}
            }}
        ],
        "summary_items": [
            {{
            "icon": "string",
            "value": "string",
            "label": "string",
            "data_type": "string"
            }}
        ]
        }}

        Use only facts from the text below — no extra info or calculations.
        Match label and data array lengths exactly.
        Use these colors in order for charts: {colors_str}
        Output ONLY JSON — no explanations or text.

        IMPORTANT:
        - Summary items should be concise top-level business KPIs.
        - Do NOT repeat any data already shown in charts within summary items.

        Analysis text:
        \"\"\"
        {analysis_text}
        \"\"\"
    """

    json_response = llm.complete(prompt=formatting_prompt).text.strip()
    dashboard = DashboardResponse.parse_raw(json_response)

    return {
        "success": True,
        "charts": dashboard.charts,
        "summary_items": dashboard.summary_items
    }
