from dotenv import load_dotenv
import os
import pandas as pd
from llama_index.experimental.query_engine import PandasQueryEngine
from llama_index.llms.openai import OpenAI
from pydantic import BaseModel
from typing import List

load_dotenv()

# Configure pandas to avoid matplotlib plotting issues
pd.options.plotting.backend = "matplotlib"
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# ----------------------------
# Patch safe_eval to fix deprecated .dt.week usage in llama_index
# ----------------------------
from llama_index.experimental import exec_utils

_original_safe_eval = exec_utils.safe_eval

def patched_safe_eval(__source, __globals=None, __locals=None):
    fixed_source = __source.replace(".dt.week", ".dt.isocalendar().week")
    return _original_safe_eval(fixed_source, __globals, __locals)

exec_utils.safe_eval = patched_safe_eval

# ----------------------------
# Global Configuration
# ----------------------------
def get_global_chart_colors():
    """
    Get global chart colors from smart dashboard configuration
    Returns the same color palette used in frontend for consistency
    """
    return [
        '#ffb366', '#ffc999', '#ffab66', '#ffd6b3', '#ff9d4d',
        '#ffe0cc', '#ffb84d', '#fff2e6', '#ffa64d', '#fff5f0',
        '#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f'
    ]

def get_semantic_colors():
    """
    Get semantic color mappings for different chart types and data meanings
    Uses global colors but assigns them semantic meaning
    """
    global_colors = get_global_chart_colors()
    return {
        # Movement types
        'inward': global_colors[4],      # '#ff9d4d' - Orange for positive/inward
        'outward': global_colors[12],    # '#e15759' - Red for negative/outward
        'neutral': global_colors[10],    # '#4e79a7' - Blue for neutral
        'positive': global_colors[14],   # '#59a14f' - Green for positive metrics
        'negative': global_colors[12],   # '#e15759' - Red for negative metrics
        'warning': global_colors[11],    # '#f28e2c' - Orange for warnings

        # Chart type defaults
        'primary': global_colors[0],     # '#ffb366' - Primary brand color
        'secondary': global_colors[1],   # '#ffc999' - Secondary brand color
        'accent': global_colors[2],      # '#ffab66' - Accent color

        # Specific use cases
        'purchase': global_colors[0],    # '#ffb366' - Purchase related
        'inventory': global_colors[13],  # '#76b7b2' - Inventory related
        'sales': global_colors[14],      # '#59a14f' - Sales related
        'spoilage': global_colors[12],   # '#e15759' - Loss/spoilage
        'transfer': global_colors[10],   # '#4e79a7' - Transfers

        # UI colors
        'border_light': '#ffffff',       # White borders
        'border_dark': '#333333',        # Dark borders
        'grid': '#e9ecef',              # Grid lines
    }

# ----------------------------
# Chart.js Data Models
# ----------------------------
class ChartDataset(BaseModel):
    label: str
    data: List[float]
    backgroundColor: List[str]
    borderColor: List[str]

class ChartData(BaseModel):
    labels: List[str]
    datasets: List[ChartDataset]

class Chart(BaseModel):
    id: str
    title: str
    type: str
    data: ChartData

class SummaryItem(BaseModel):
    icon: str
    value: str
    label: str
    data_type: str

class DashboardResponse(BaseModel):
    charts: List[Chart]
    summary_items: List[SummaryItem]

# ----------------------------
# Main Dashboard Function
# ----------------------------
def generate_purchase_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate Purchase Department dashboard with fixed charts and summary cards
    without using LLM - direct DataFrame analysis
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Purchase Records",
                        "data_type": "text"
                    }
                ]
            }

        # 1. Total Purchase Cost (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns:
            total_cost = df['Total(incl.tax,etc)'].sum()
            summary_items.append({
                "icon": "currency_rupee",
                "value": f"₹{total_cost:,.2f}",
                "label": "Total Purchase Cost",
                "data_type": "currency"
            })

        # 2. Total Items Purchased (Summary Item)
        if 'Received Qty' in df.columns:
            total_items = df['Received Qty'].sum()
            summary_items.append({
                "icon": "inventory",
                "value": f"{total_items:,.0f}",
                "label": "Total Items Received",
                "data_type": "number"
            })

        # 3. Average Cost per Item (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            avg_cost = total_cost / total_items if total_items > 0 else 0
            summary_items.append({
                "icon": "calculate",
                "value": f"₹{avg_cost:.2f}",
                "label": "Average Cost per Item",
                "data_type": "currency"
            })

        # 4. Purchase Order Accuracy (Summary Item)
        if 'Order Qty' in df.columns and 'Received Qty' in df.columns:
            total_ordered = df['Order Qty'].sum()
            total_received = df['Received Qty'].sum()
            accuracy = (total_received / total_ordered * 100) if total_ordered > 0 else 0
            summary_items.append({
                "icon": "check_circle",
                "value": f"{accuracy:.1f}%",
                "label": "Purchase Order Accuracy",
                "data_type": "percentage"
            })

        # 5. Purchase Cost by Location (Bar Chart)
        if 'Location' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            location_costs = df.groupby('Location')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not location_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_location",
                    "title": "Purchase Cost by Location",
                    "type": "bar",
                    "data": {
                        "labels": location_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": location_costs.values.tolist(),
                            "backgroundColor": colors[:len(location_costs)],
                            "borderColor": colors[:len(location_costs)]
                        }]
                    }
                })

        # 6. Purchase Cost by Vendor (Bar Chart)
        if 'Vendor Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            vendor_costs = df.groupby('Vendor Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(10)
            if not vendor_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_vendor",
                    "title": "Top 10 Vendors by Purchase Cost",
                    "type": "bar",
                    "data": {
                        "labels": vendor_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": vendor_costs.values.tolist(),
                            "backgroundColor": colors[:len(vendor_costs)],
                            "borderColor": colors[:len(vendor_costs)]
                        }]
                    }
                })

        # 7. Purchase by Category (Doughnut Chart)
        if 'Category' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            category_costs = df.groupby('Category')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not category_costs.empty:
                charts.append({
                    "id": "purchase_by_category",
                    "title": "Purchase Distribution by Category",
                    "type": "doughnut",
                    "data": {
                        "labels": category_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": category_costs.values.tolist(),
                            "backgroundColor": colors[:len(category_costs)],
                            "borderColor": colors[:len(category_costs)]
                        }]
                    }
                })

        # 8. Cost Trends Over Time (Line Chart)
        # Look for date columns - GRN has specific date column names
        date_columns = []
        for col in df.columns:
            if any(date_word in col.lower() for date_word in ['date', 'entry']):
                date_columns.append(col)

        if date_columns and 'Total(incl.tax,etc)' in df.columns:
            date_col = date_columns[0]  # Use first date column found
            try:
                df_copy = df.copy()
                # Handle different date formats
                if df_copy[date_col].dtype == 'object':
                    df_copy[date_col] = pd.to_datetime(df_copy[date_col], errors='coerce')
                daily_costs = df_copy.groupby(df_copy[date_col].dt.date)['Total(incl.tax,etc)'].sum().sort_index()
                if len(daily_costs) > 1:
                    charts.append({
                        "id": "cost_trends",
                        "title": "Purchase Cost Trends Over Time",
                        "type": "line",
                        "data": {
                            "labels": [str(date) for date in daily_costs.index],
                            "datasets": [{
                                "label": "Daily Purchase Cost (₹)",
                                "data": daily_costs.values.tolist(),
                                "backgroundColor": [semantic_colors['purchase']],
                                "borderColor": [semantic_colors['purchase']]
                            }]
                        }
                    })
            except Exception as e:
                print(f"Error creating cost trends chart: {e}")
                pass  # Skip if date parsing fails

        # 9. Top 15 Purchase Items (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            item_costs = df.groupby('Item Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(15)
            if not item_costs.empty:
                charts.append({
                    "id": "top_purchase_items",
                    "title": "Top 15 Purchase Items by Cost",
                    "type": "bar",
                    "data": {
                        "labels": item_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": item_costs.values.tolist(),
                            "backgroundColor": colors[:len(item_costs)],
                            "borderColor": colors[:len(item_costs)]
                        }]
                    }
                })

        # 10. Cost per Item Analysis (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            df_filtered = df[df['Received Qty'] > 0].copy()
            df_filtered['Cost_per_Item'] = df_filtered['Total(incl.tax,etc)'] / df_filtered['Received Qty']
            cost_per_item = df_filtered.groupby('Item Name')['Cost_per_Item'].mean().sort_values(ascending=False).head(10)
            if not cost_per_item.empty:
                charts.append({
                    "id": "cost_per_item",
                    "title": "Top 10 Items by Average Cost per Unit",
                    "type": "bar",
                    "data": {
                        "labels": cost_per_item.index.tolist(),
                        "datasets": [{
                            "label": "Cost per Unit (₹)",
                            "data": cost_per_item.values.tolist(),
                            "backgroundColor": colors[:len(cost_per_item)],
                            "borderColor": colors[:len(cost_per_item)]
                        }]
                    }
                })

        # 11. Total Vendors Count (Summary Item)
        if 'Vendor Name' in df.columns:
            unique_vendors = df['Vendor Name'].nunique()
            summary_items.append({
                "icon": "business",
                "value": f"{unique_vendors}",
                "label": "Active Vendors",
                "data_type": "number"
            })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating purchase dashboard: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def generate_inventory_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate comprehensive Inventory Management dashboard based on store_variance DataFrame
    Focused on essential business insights with non-redundant, high-value charts
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Inventory Records",
                        "data_type": "text"
                    }
                ]
            }

        # Calculate derived metrics for period analysis
        df_calc = df.copy()

        # Calculate Return To Store In Amount (items sent back TO store from work areas)
        df_calc['Return To Store In Amount'] = (
            df_calc.get('Return To Store In Qty', 0) * df_calc.get('WAC(incl.tax,etc)', 0)
        )

        # Calculate period movements with correct business logic
        df_calc['Period_Net_Inward'] = (
            df_calc.get('Purchase Amount', 0) +
            df_calc.get('Ibt In Amount', 0) +
            df_calc.get('Return To Store In Amount', 0)  # Items returned TO store (inward)
        )

        df_calc['Period_Net_Outward'] = (
            df_calc.get('Return-Qty Amount', 0) +  # Items returned to vendor (outward)
            df_calc.get('Indent Amount', 0) +
            df_calc.get('Ibt Out Amount', 0) +
            df_calc.get('Spoilage Amount', 0)
        )

        df_calc['Period_Net_Movement'] = df_calc['Period_Net_Inward'] - df_calc['Period_Net_Outward']

        # Turnover calculation (more robust)
        df_calc['Avg_Inventory'] = (df_calc.get('Opening Amount', 0) + df_calc.get('Closing Amount', 0)) / 2
        df_calc['Period_Turnover'] = df_calc.apply(
            lambda row: row['Period_Net_Outward'] / row['Avg_Inventory'] if row['Avg_Inventory'] > 0 else 0, axis=1
        )

        # Growth rate calculation
        df_calc['Period_Growth_Rate'] = df_calc.apply(
            lambda row: ((row.get('Closing Amount', 0) - row.get('Opening Amount', 0)) /
                        row.get('Opening Amount', 1)) * 100 if row.get('Opening Amount', 0) > 0 else 0, axis=1
        )

        # ABC Classification based on closing value
        df_calc = df_calc.sort_values('Closing Amount', ascending=False)
        total_value = df_calc['Closing Amount'].sum()
        df_calc['Cumulative_Percentage'] = (df_calc['Closing Amount'].cumsum() / total_value) * 100
        df_calc['ABC_Class'] = df_calc['Cumulative_Percentage'].apply(
            lambda x: 'A' if x <= 70 else ('B' if x <= 90 else 'C')
        )

        # === COMPREHENSIVE INVENTORY MOVEMENT KPIs (15 Cards) ===

        # Calculate all movement types - Convert to native Python types
        total_opening = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
        total_closing = float(df_calc.get('Closing Amount', pd.Series([0])).sum())
        net_growth = float(((total_closing - total_opening) / total_opening) * 100) if total_opening > 0 else 0.0

        total_purchases = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
        total_indents = float(df_calc.get('Indent Amount', pd.Series([0])).sum())
        total_ibt_in = float(df_calc.get('Ibt In Amount', pd.Series([0])).sum())
        total_ibt_out = float(df_calc.get('Ibt Out Amount', pd.Series([0])).sum())
        total_spoilage = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
        total_returns_to_store = float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum())
        total_returns_to_vendor = float(df_calc.get('Return-Qty Amount', pd.Series([0])).sum())

        total_inward = float(df_calc['Period_Net_Inward'].sum())
        total_outward = float(df_calc['Period_Net_Outward'].sum())
        avg_turnover = float(df_calc['Period_Turnover'].mean()) if not df_calc['Period_Turnover'].empty else 0.0

        # Row 1: Stock Position
        summary_items.extend([
            {
                "icon": "trending_up",
                "value": f"₹{total_opening:,.0f}",
                "label": "Opening Stock Value",
                "data_type": "currency"
            },
            {
                "icon": "account_balance_wallet",
                "value": f"₹{total_closing:,.0f}",
                "label": "Closing Stock Value",
                "data_type": "currency"
            },
            {
                "icon": "percent" if net_growth >= 0 else "trending_down",
                "value": f"{net_growth:+.1f}%",
                "label": "Stock Growth %",
                "data_type": "percentage"
            }
        ])

        # Row 2: Inward Movements (Items Coming IN)
        summary_items.extend([
            {
                "icon": "shopping_cart",
                "value": f"₹{total_purchases:,.0f}",
                "label": "Purchases (from Vendor)",
                "data_type": "currency"
            },
            {
                "icon": "input",
                "value": f"₹{total_ibt_in:,.0f}",
                "label": "IBT In (from Locations)",
                "data_type": "currency"
            },
            {
                "icon": "keyboard_return",
                "value": f"₹{total_returns_to_store:,.0f}",
                "label": "Returns to Store (from Work Areas)",
                "data_type": "currency"
            }
        ])

        # Row 3: Outward Movements (Items Going OUT)
        summary_items.extend([
            {
                "icon": "construction",
                "value": f"₹{total_indents:,.0f}",
                "label": "Indents/Usage (Consumed)",
                "data_type": "currency"
            },
            {
                "icon": "output",
                "value": f"₹{total_ibt_out:,.0f}",
                "label": "IBT Out (to Locations)",
                "data_type": "currency"
            },
            {
                "icon": "undo",
                "value": f"₹{total_returns_to_vendor:,.0f}",
                "label": "Returns to Vendor",
                "data_type": "currency"
            }
        ])

        # Row 4: Loss & Efficiency Metrics
        active_skus = int(len(df_calc[df_calc['Period_Net_Outward'] > 0]))
        total_skus = int(len(df_calc))
        spoilage_rate = float((total_spoilage / total_outward * 100)) if total_outward > 0 else 0.0

        summary_items.extend([
            {
                "icon": "warning",
                "value": f"₹{total_spoilage:,.0f}",
                "label": "Spoilage/Loss",
                "data_type": "currency"
            },
            {
                "icon": "trending_down",
                "value": f"{spoilage_rate:.1f}%",
                "label": "Spoilage Rate",
                "data_type": "percentage"
            },
            {
                "icon": "inventory",
                "value": f"{active_skus}/{total_skus}",
                "label": "Active SKUs",
                "data_type": "text"
            }
        ])

        # Row 5: Summary Totals & Performance
        net_movement = total_inward - total_outward
        summary_items.extend([
            {
                "icon": "arrow_downward",
                "value": f"₹{total_inward:,.0f}",
                "label": "Total Inward Movement",
                "data_type": "currency"
            },
            {
                "icon": "arrow_upward",
                "value": f"₹{total_outward:,.0f}",
                "label": "Total Outward Movement",
                "data_type": "currency"
            },
            {
                "icon": "sync",
                "value": f"{avg_turnover:.2f}x",
                "label": "Avg Turnover Rate",
                "data_type": "number"
            }
        ])

        # === COMPREHENSIVE INVENTORY DASHBOARD CHARTS ===

        # 1. Complete Inventory Flow Tracking (Enhanced Waterfall) - Shows every rupee movement
        if all(col in df.columns for col in ['Opening Amount', 'Purchase Amount', 'Indent Amount', 'Spoilage Amount', 'Closing Amount']):
            # Calculate all movements with proper visibility for small amounts
            opening_val = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
            purchases_val = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
            ibt_in_val = float(df_calc.get('Ibt In Amount', pd.Series([0])).sum())
            returns_in_val = float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum())  # Items returned TO store
            returns_out_val = float(df_calc.get('Return-Qty Amount', pd.Series([0])).sum())  # Items returned to vendor
            consumption_val = float(df_calc.get('Indent Amount', pd.Series([0])).sum())
            ibt_out_val = float(df_calc.get('Ibt Out Amount', pd.Series([0])).sum())
            spoilage_val = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
            closing_val = float(df_calc.get('Closing Amount', pd.Series([0])).sum())

            # Ensure spoilage is visible even if small - minimum 1% of total flow
            total_flow = opening_val + purchases_val + ibt_in_val + returns_in_val
            min_visible = total_flow * 0.01 if total_flow > 0 else 100
            spoilage_display = max(spoilage_val, min_visible) if spoilage_val > 0 else spoilage_val

            flow_data = {
                'Opening Stock': opening_val,
                '+ Purchases': purchases_val,
                '+ IBT Received': ibt_in_val,
                '+ Returns to Store': returns_in_val,  # Items returned TO store from work areas
                '- Returns to Vendor': -returns_out_val,  # Items returned to vendor
                '- Consumption/Usage': -consumption_val,
                '- IBT Sent Out': -ibt_out_val,
                '- Spoilage/Loss': -spoilage_display,
                'Closing Stock': closing_val
            }

            waterfall_labels = list(flow_data.keys())
            waterfall_values = [float(val) for val in flow_data.values()]

            charts.append({
                "id": "complete_inventory_flow",
                "title": "Complete Inventory Movement Tracking (₹)",
                "subtitle": "Track every rupee: Opening → All Inflows → All Outflows → Closing",
                "type": "bar",
                "data": {
                    "labels": waterfall_labels,
                    "datasets": [{
                        "label": "Amount (₹)",
                        "data": waterfall_values,
                        "backgroundColor": [
                            '#4A90E2',  # Opening - Blue
                            '#7ED321',  # Purchases - Green
                            '#50E3C2',  # IBT In - Teal
                            '#B8E986',  # Returns to Store - Light Green (inward)
                            '#E74C3C',  # Returns to Vendor - Red (outward)
                            '#F5A623',  # Consumption - Orange
                            '#D0021B',  # IBT Out - Red
                            '#9013FE',  # Spoilage - Purple (distinct for visibility)
                            '#4A90E2'   # Closing - Blue
                        ],
                        "borderColor": '#FFFFFF',
                        "borderWidth": 2,
                        "borderRadius": 6
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "plugins": {
                        "legend": {"display": False},
                        "title": {
                            "display": True,
                            "text": "Every movement tracked - No inventory unaccounted",
                            "font": {"size": 12, "style": "italic"},
                            "color": "#666"
                        },
                        "tooltip": {
                            "callbacks": {
                                "title": "function(context) { return context[0].label; }",
                                "label": "function(context) { var val = Math.abs(context.parsed.y); var actual = " + str(spoilage_val) + "; if(context.label.includes('Spoilage') && actual !== val) { return 'Actual Spoilage: ₹' + actual.toLocaleString() + ' (Enhanced for visibility)'; } return 'Amount: ₹' + val.toLocaleString(); }",
                                "afterLabel": "function(context) { if(context.parsed.y > 0) return '↗️ Inflow'; else if(context.parsed.y < 0) return '↘️ Outflow'; else return '📊 Stock Level'; }"
                            }
                        }
                    },
                    "scales": {
                        "x": {
                            "grid": {"display": False},
                            "ticks": {
                                "font": {"size": 10},
                                "maxRotation": 45
                            }
                        },
                        "y": {
                            "grid": {"color": "#E5E5E5"},
                            "ticks": {
                                "font": {"size": 10},
                                "callback": "function(value) { return '₹' + (Math.abs(value)/1000).toFixed(0) + 'K'; }"
                            }
                        }
                    }
                }
            })

        # 2. Top 12 High-Value Items Analysis (Critical inventory focus)
        if 'Item Name' in df.columns and 'Closing Amount' in df.columns:
            # Get top 12 items by current value
            top_items_analysis = df_calc.nlargest(12, 'Closing Amount')[
                ['Item Name', 'Item Code', 'Category', 'Closing Amount', 'Opening Amount', 'Period_Turnover']
            ].copy()

            if not top_items_analysis.empty:
                # Create detailed info text for high-value items
                high_value_info_text = """
                💎 Top High-Value Items represent your most valuable inventory assets:

                🎯 Why This Matters:
                • These 12 items likely represent 40-60% of your total inventory value
                • Small changes in these items have big impact on your business
                • Stockouts here can significantly affect operations and revenue

                📊 What to Monitor:
                • Current vs Opening Stock: Are levels increasing or decreasing?
                • Turnover Rate: How fast are these valuable items moving?
                • Category Distribution: Which categories dominate your high-value inventory?

                💡 Management Actions:
                • High Current Stock: Ensure it's not dead stock, check demand patterns
                • Low Current Stock: Risk of stockout, review reorder points
                • Low Turnover: Investigate if it's seasonal or needs promotion
                • High Turnover: Ensure adequate supply chain to avoid stockouts
                """

                item_names = [name[:25] + '...' if len(name) > 25 else name for name in top_items_analysis['Item Name'].tolist()]

                charts.append({
                    "id": "top_high_value_items",
                    "title": "Top 12 High-Value Items: Your Most Critical Inventory Assets",
                    "subtitle": "Focus on these items - they drive the majority of your inventory value",
                    "info": high_value_info_text,
                    "type": "bar",
                    "data": {
                        "labels": item_names,
                        "datasets": [
                            {
                                "label": "💰 Current Stock Value (₹)",
                                "data": [float(val) for val in top_items_analysis['Closing Amount'].round(2).tolist()],
                                "backgroundColor": '#E74C3C',
                                "borderColor": '#C0392B',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "📊 Opening Stock Value (₹)",
                                "data": [float(val) for val in top_items_analysis['Opening Amount'].round(2).tolist()],
                                "backgroundColor": '#3498DB',
                                "borderColor": '#2980B9',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12}
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return '" + str(top_items_analysis['Item Name'].tolist()) + "'[context[0].dataIndex]; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var current = context[0].parsed.y; var opening = context[1] ? context[1].parsed.y : 0; var change = opening > 0 ? ((current - opening) / opening * 100).toFixed(1) : 'N/A'; var turnover = [" + str([float(t) for t in top_items_analysis['Period_Turnover'].tolist()]) + "][context[0].dataIndex]; var category = [" + str(top_items_analysis['Category'].tolist()) + "][context[0].dataIndex]; return 'Change: ' + (change !== 'N/A' ? (change > 0 ? '+' : '') + change + '%' : 'N/A') + ' | Turnover: ' + turnover.toFixed(2) + 'x | Category: ' + category; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {
                                    "font": {"size": 9},
                                    "maxRotation": 45
                                }
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 3. Inventory Value Distribution by Category (Clear business breakdown)
        if 'Category' in df.columns and 'Closing Amount' in df.columns:
            category_analysis = df_calc.groupby('Category').agg({
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Item Code': 'count'
            }).sort_values('Closing Amount', ascending=False)

            if not category_analysis.empty:
                # Take top 8 categories for clarity
                top_categories = category_analysis.head(8)

                charts.append({
                    "id": "inventory_by_category",
                    "title": "Inventory Value by Category: Business Portfolio Overview",
                    "subtitle": "Understand which product categories drive your inventory investment",
                    "type": "bar",
                    "data": {
                        "labels": top_categories.index.tolist(),
                        "datasets": [
                            {
                                "label": "Current Stock Value (₹)",
                                "data": [float(val) for val in top_categories['Closing Amount'].round(2).tolist()],
                                "backgroundColor": '#4A90E2',
                                "borderColor": '#357ABD',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "Opening Stock Value (₹)",
                                "data": [float(val) for val in top_categories['Opening Amount'].round(2).tolist()],
                                "backgroundColor": '#7ED321',
                                "borderColor": '#5CB85C',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12}
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return 'Category: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var current = context[0].parsed.y; var opening = context[1] ? context[1].parsed.y : 0; var change = opening > 0 ? ((current - opening) / opening * 100).toFixed(1) : 'N/A'; return 'Change: ' + (change !== 'N/A' ? (change > 0 ? '+' : '') + change + '%' : 'N/A'); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 3. ABC Classification Analysis (Critical for inventory prioritization)
        if 'ABC_Class' in df_calc.columns:
            abc_analysis = df_calc.groupby('ABC_Class').agg({
                'Closing Amount': 'sum',
                'Item Code': 'count',
                'Period_Turnover': 'mean'
            }).reset_index()
            abc_analysis.columns = ['ABC_Class', 'Total_Value', 'Item_Count', 'Avg_Turnover']

            if not abc_analysis.empty:
                # Create detailed info text for ABC analysis
                abc_info_text = """
                📊 ABC Analysis helps you prioritize inventory management:

                🔴 Class A Items (70% of total value):
                • Your most valuable inventory items
                • Require daily monitoring and tight control
                • Focus on accurate forecasting and supplier relationships
                • Any stockout here significantly impacts business

                🟡 Class B Items (20% of total value):
                • Moderate value items with regular movement
                • Review weekly and maintain adequate safety stock
                • Good candidates for automated reordering systems

                🔵 Class C Items (10% of total value):
                • Low value but may be high volume items
                • Monthly reviews are sufficient
                • Can afford higher safety stock levels
                • Focus on cost-effective procurement
                """

                charts.append({
                    "id": "abc_classification_analysis",
                    "title": "ABC Analysis: Smart Inventory Prioritization Strategy",
                    "subtitle": "Focus your time and resources on what matters most - 80/20 rule in action",
                    "info": abc_info_text,
                    "type": "doughnut",
                    "data": {
                        "labels": [
                            f"🔴 Class A: {abc_analysis.iloc[0]['Item_Count']} items (70% Value - Critical)",
                            f"🟡 Class B: {abc_analysis.iloc[1]['Item_Count']} items (20% Value - Important)",
                            f"🔵 Class C: {abc_analysis.iloc[2]['Item_Count']} items (10% Value - Routine)"
                        ] if len(abc_analysis) == 3 else [f"Class {row['ABC_Class']}: {row['Item_Count']} items" for _, row in abc_analysis.iterrows()],
                        "datasets": [{
                            "label": "Inventory Value (₹)",
                            "data": [float(val) for val in abc_analysis['Total_Value'].round(2).tolist()],
                            "backgroundColor": ['#E74C3C', '#F39C12', '#3498DB'],  # Red, Orange, Blue
                            "borderColor": '#FFFFFF',
                            "borderWidth": 3,
                            "hoverBorderWidth": 4
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "cutout": '45%',
                        "plugins": {
                            "legend": {
                                "position": "bottom",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 11},
                                    "boxWidth": 12
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return context.label.split(':')[1]; }",
                                    "label": "function(context) { var total = context.dataset.data.reduce((a, b) => a + b, 0); var percentage = ((context.parsed / total) * 100).toFixed(1); return 'Value: ₹' + context.parsed.toLocaleString() + ' (' + percentage + '% of total inventory)'; }",
                                    "afterLabel": "function(context) { var turnover = [" + str([float(t) for t in abc_analysis['Avg_Turnover'].tolist()]) + "][context.dataIndex]; var priority = ['Daily Focus', 'Weekly Review', 'Monthly Check'][context.dataIndex]; return 'Avg Turnover: ' + turnover.toFixed(2) + 'x | Priority: ' + priority; }"
                                }
                            }
                        }
                    }
                })

        # 4. Movement Pattern Analysis (Inflow vs Outflow by Category)
        if 'Category' in df.columns:
            movement_analysis = df_calc.groupby('Category').agg({
                'Purchase Amount': 'sum',
                'Ibt In Amount': 'sum',
                'Return To Store In Amount': 'sum',  # Returns TO store (inward)
                'Return-Qty Amount': 'sum',  # Returns to vendor (outward)
                'Indent Amount': 'sum',
                'Ibt Out Amount': 'sum',
                'Spoilage Amount': 'sum'
            }).fillna(0)

            # Calculate total inflow and outflow with correct business logic
            movement_analysis['Total_Inflow'] = (
                movement_analysis['Purchase Amount'] +
                movement_analysis['Ibt In Amount'] +
                movement_analysis['Return To Store In Amount']  # Items returned TO store (inward)
            )
            movement_analysis['Total_Outflow'] = (
                movement_analysis['Return-Qty Amount'] +  # Items returned to vendor (outward)
                movement_analysis['Indent Amount'] +
                movement_analysis['Ibt Out Amount'] +
                movement_analysis['Spoilage Amount']
            )

            if not movement_analysis.empty:
                # Take top 6 categories by total movement
                movement_analysis['Total_Movement'] = movement_analysis['Total_Inflow'] + movement_analysis['Total_Outflow']
                top_movement_categories = movement_analysis.nlargest(6, 'Total_Movement')

                charts.append({
                    "id": "movement_pattern_analysis",
                    "title": "Inventory Movement Patterns: Inflow vs Outflow",
                    "subtitle": "Understand which categories have high activity and movement velocity",
                    "type": "bar",
                    "data": {
                        "labels": top_movement_categories.index.tolist(),
                        "datasets": [
                            {
                                "label": "📈 Total Inflow (Purchases + IBT In + Returns to Store)",
                                "data": [float(val) for val in top_movement_categories['Total_Inflow'].round(2).tolist()],
                                "backgroundColor": '#2ECC71',
                                "borderColor": '#27AE60',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "📉 Total Outflow (Returns to Vendor + Usage + IBT Out + Spoilage)",
                                "data": [float(val) for val in top_movement_categories['Total_Outflow'].round(2).tolist()],
                                "backgroundColor": '#E74C3C',
                                "borderColor": '#C0392B',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return 'Category: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var inflow = context[0].parsed.y; var outflow = context[1] ? context[1].parsed.y : 0; var velocity = ((inflow + outflow) / 2).toFixed(0); return 'Movement Velocity: ₹' + velocity + 'K'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Amount (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 5. Turnover Performance Analysis (Operational efficiency indicator)
        if 'Category' in df.columns and 'Period_Turnover' in df_calc.columns:
            turnover_by_category = df_calc.groupby('Category').agg({
                'Period_Turnover': 'mean',
                'Closing Amount': 'sum',
                'Item Code': 'count'
            }).sort_values('Period_Turnover', ascending=False)

            if not turnover_by_category.empty:
                # Take top 8 categories for better visualization
                top_turnover_categories = turnover_by_category.head(8)
                categories = top_turnover_categories.index.tolist()
                turnover_rates = [float(val) for val in top_turnover_categories['Period_Turnover'].round(3).tolist()]

                # Create detailed info text for turnover analysis
                turnover_info_text = """
                📈 Inventory Turnover Rate measures how efficiently you're using your inventory:

                🔢 What it means:
                • Turnover Rate = How many times inventory is sold/used in a period
                • Higher turnover = Better cash flow and fresher inventory
                • Lower turnover = Money tied up in slow-moving stock

                🎯 Performance Benchmarks:
                • 🌟 Excellent (>3.0x): Outstanding performance, great demand forecasting
                • ✅ Good (2.0-3.0x): Healthy turnover, well-managed inventory
                • ⚠️ Average (1.0-2.0x): Room for improvement, review demand patterns
                • 🔴 Poor (<1.0x): Slow-moving inventory, needs immediate attention

                💡 Action Items:
                • High turnover: Ensure adequate stock to avoid stockouts
                • Low turnover: Review pricing, promotions, or discontinue slow items
                """

                # Color code based on turnover performance with clear thresholds
                turnover_colors = []
                performance_labels = []
                for rate in turnover_rates:
                    if rate > 3.0:
                        turnover_colors.append('#27AE60')  # Excellent - Dark Green
                        performance_labels.append('🌟 Excellent')
                    elif rate > 2.0:
                        turnover_colors.append('#2ECC71')  # Good - Green
                        performance_labels.append('✅ Good')
                    elif rate > 1.0:
                        turnover_colors.append('#F39C12')  # Average - Orange
                        performance_labels.append('⚠️ Average')
                    else:
                        turnover_colors.append('#E74C3C')  # Poor - Red
                        performance_labels.append('🔴 Needs Attention')

                charts.append({
                    "id": "turnover_performance_analysis",
                    "title": "Inventory Turnover Performance: Cash Flow Efficiency Meter",
                    "subtitle": "Measure how fast your inventory converts to sales - Key performance indicator",
                    "info": turnover_info_text,
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [{
                            "label": "Turnover Rate (times per period)",
                            "data": turnover_rates,
                            "backgroundColor": turnover_colors,
                            "borderColor": '#FFFFFF',
                            "borderWidth": 2,
                            "borderRadius": 6
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "title": {
                                "display": True,
                                "text": "🎯 Target: >2.0x = Good Performance | >3.0x = Excellent Performance",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return 'Category: ' + context[0].label; }",
                                    "label": "function(context) { var rate = context.parsed.y; var performance = [" + str(performance_labels) + "][context.dataIndex]; return 'Turnover Rate: ' + rate.toFixed(2) + 'x (' + performance + ')'; }",
                                    "afterLabel": "function(context) { var items = [" + str([int(c) for c in top_turnover_categories['Item Code'].tolist()]) + "][context.dataIndex]; var interpretation = context.parsed.y > 2 ? 'Fast-moving, good cash flow' : 'Slow-moving, review needed'; return 'Items in category: ' + items + ' | ' + interpretation; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Turnover Rate (x times per period)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + 'x'; }"
                                }
                            }
                        }
                    }
                })

        # 6. Top Value Items Performance Matrix
        if 'Item Name' in df.columns and 'Closing Amount' in df.columns:
            # Get top 12 items by value with comprehensive metrics
            top_items = df_calc.nlargest(12, 'Closing Amount')[
                ['Item Name', 'Category', 'Closing Amount', 'Opening Amount', 'Period_Turnover']
            ].copy()

            if not top_items.empty:
                # Calculate value change
                top_items['Value_Change'] = top_items['Closing Amount'] - top_items['Opening Amount']
                item_names = [name[:15] + "..." if len(name) > 15 else name for name in top_items['Item Name'].tolist()]

                charts.append({
                    "id": "top_value_items_matrix",
                    "title": "Top 12 High-Value Items: Current vs Opening Stock",
                    "subtitle": "Monitor your most valuable inventory items and their stock changes",
                    "type": "horizontalBar",
                    "data": {
                        "labels": item_names,
                        "datasets": [
                            {
                                "label": "💰 Current Stock Value (₹)",
                                "data": [float(val) for val in top_items['Closing Amount'].round(2).tolist()],
                                "backgroundColor": '#3498DB',
                                "borderColor": '#2980B9',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "📊 Opening Stock Value (₹)",
                                "data": [float(val) for val in top_items['Opening Amount'].round(2).tolist()],
                                "backgroundColor": '#95A5A6',
                                "borderColor": '#7F8C8D',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "indexAxis": "y",
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return 'Item: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.x.toLocaleString(); }",
                                    "afterBody": "function(context) { var current = context[0].parsed.x; var opening = context[1] ? context[1].parsed.x : 0; var change = current - opening; var changePercent = opening > 0 ? ((change / opening) * 100).toFixed(1) : 'N/A'; return 'Value Change: ₹' + change.toLocaleString() + ' (' + (change > 0 ? '+' : '') + changePercent + '%)'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            },
                            "y": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 9}}
                            }
                        }
                    }
                })

        # 7. Location Performance Comparison
        if 'Location' in df.columns:
            location_performance = df_calc.groupby('Location').agg({
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Period_Turnover': 'mean',
                'Spoilage Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            if not location_performance.empty and len(location_performance) > 1:

                charts.append({
                    "id": "location_performance_comparison",
                    "title": "Location-wise Inventory Performance Comparison",
                    "subtitle": "Compare inventory value, turnover, and efficiency across locations",
                    "type": "radar",
                    "data": {
                        "labels": [
                            "Current Stock Value",
                            "Stock Growth %",
                            "Turnover Rate",
                            "Spoilage Control",
                            "Item Variety"
                        ],
                        "datasets": [
                            {
                                "label": location,
                                "data": [
                                    float(row['Closing Amount'] / location_performance['Closing Amount'].max() * 100),
                                    float(((row['Closing Amount'] - row['Opening Amount']) / row['Opening Amount'] * 100) if row['Opening Amount'] > 0 else 0),
                                    float(row['Period_Turnover'] * 20),  # Scale for visibility
                                    float(100 - (row['Spoilage Amount'] / row['Closing Amount'] * 100)) if row['Closing Amount'] > 0 else 100,
                                    float(row['Item Code'] / location_performance['Item Code'].max() * 100)
                                ],
                                "backgroundColor": colors[i % len(colors)] + '40',  # Semi-transparent
                                "borderColor": colors[i % len(colors)],
                                "borderWidth": 2,
                                "pointBackgroundColor": colors[i % len(colors)],
                                "pointBorderColor": '#FFFFFF',
                                "pointBorderWidth": 2,
                                "pointRadius": 4
                            } for i, (location, row) in enumerate(location_performance.iterrows())
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 11}
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return context[0].dataset.label + ' - ' + context[0].label; }",
                                    "label": "function(context) { var labels = ['Stock Value', 'Growth %', 'Turnover Rate', 'Spoilage Control', 'Item Variety']; return labels[context.dataIndex] + ': ' + context.parsed.r.toFixed(1) + '%'; }"
                                }
                            }
                        },
                        "scales": {
                            "r": {
                                "beginAtZero": True,
                                "max": 100,
                                "grid": {"color": "#E5E5E5"},
                                "pointLabels": {"font": {"size": 10}},
                                "ticks": {
                                    "display": False,
                                    "stepSize": 20
                                }
                            }
                        }
                    }
                })

        # 8. Critical Risk & Opportunity Analysis
        if 'Spoilage Amount' in df.columns and 'Category' in df.columns:
            # Comprehensive risk analysis combining multiple factors
            risk_analysis = df_calc.groupby('Category').agg({
                'Spoilage Amount': 'sum',
                'Period_Turnover': 'mean',
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Item Code': 'count'
            }).fillna(0)

            # Calculate comprehensive risk metrics
            risk_analysis['Spoilage_Rate'] = (risk_analysis['Spoilage Amount'] / risk_analysis['Closing Amount'] * 100).fillna(0)
            risk_analysis['Turnover_Risk'] = risk_analysis['Period_Turnover'].apply(lambda x: 10 if x < 0.5 else (5 if x < 1.0 else (2 if x < 2.0 else 0)))
            risk_analysis['Value_Risk'] = (risk_analysis['Closing Amount'] / risk_analysis['Closing Amount'].sum() * 100).apply(lambda x: 10 if x > 20 else (5 if x > 10 else 0))
            risk_analysis['Overall_Risk_Score'] = risk_analysis['Spoilage_Rate'] + risk_analysis['Turnover_Risk'] + risk_analysis['Value_Risk']

            # Get categories with significant risk (top 6)
            high_risk_categories = risk_analysis[risk_analysis['Overall_Risk_Score'] > 0].nlargest(6, 'Overall_Risk_Score')

            if not high_risk_categories.empty:
                categories = high_risk_categories.index.tolist()
                spoilage_rates = [float(val) for val in high_risk_categories['Spoilage_Rate'].round(2).tolist()]
                risk_scores = [float(val) for val in high_risk_categories['Overall_Risk_Score'].round(1).tolist()]

                # Color code based on comprehensive risk level
                risk_colors = []
                risk_labels = []
                for score in risk_scores:
                    if score > 20:
                        risk_colors.append('#C0392B')  # Critical - Dark Red
                        risk_labels.append('🚨 Critical')
                    elif score > 15:
                        risk_colors.append('#E74C3C')  # High - Red
                        risk_labels.append('🔴 High Risk')
                    elif score > 10:
                        risk_colors.append('#F39C12')  # Medium - Orange
                        risk_labels.append('⚠️ Medium Risk')
                    else:
                        risk_colors.append('#F1C40F')  # Low - Yellow
                        risk_labels.append('⚡ Watch')

                charts.append({
                    "id": "comprehensive_risk_analysis",
                    "title": "Critical Risk & Opportunity Analysis by Category",
                    "subtitle": "Identifies categories needing immediate attention: spoilage + slow turnover + high value",
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "🔍 Spoilage Rate (%)",
                                "data": spoilage_rates,
                                "backgroundColor": risk_colors,
                                "borderColor": '#FFFFFF',
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y'
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "title": {
                                "display": True,
                                "text": "Focus on categories with highest risk scores for immediate action",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return 'Category: ' + context[0].label; }",
                                    "label": "function(context) { var spoilageRate = context.parsed.y; var riskLabels = " + str(risk_labels) + "; var riskScores = " + str(risk_scores) + "; return 'Spoilage Rate: ' + spoilageRate.toFixed(2) + '%'; }",
                                    "afterLabel": "function(context) { var riskLabels = " + str(risk_labels) + "; var riskScores = " + str(risk_scores) + "; return 'Risk Level: ' + riskLabels[context.dataIndex] + ' (Score: ' + riskScores[context.dataIndex] + ')'; }",
                                    "footer": "function(context) { return 'Action: Review inventory levels, check expiry dates, improve turnover'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Spoilage Rate (%)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + '%'; }"
                                }
                            }
                        }
                    }
                })

        # 9. Stock Level Health Check (Critical for operations)
        if 'Closing Amount' in df.columns and 'Period_Turnover' in df_calc.columns:
            # Categorize items by stock health
            df_calc['Stock_Health'] = 'Unknown'
            df_calc.loc[df_calc['Period_Turnover'] > 2, 'Stock_Health'] = '🟢 Healthy (Fast Moving)'
            df_calc.loc[(df_calc['Period_Turnover'] <= 2) & (df_calc['Period_Turnover'] > 1), 'Stock_Health'] = '🟡 Moderate (Average Moving)'
            df_calc.loc[(df_calc['Period_Turnover'] <= 1) & (df_calc['Period_Turnover'] > 0), 'Stock_Health'] = '🟠 Slow (Needs Attention)'
            df_calc.loc[df_calc['Period_Turnover'] == 0, 'Stock_Health'] = '🔴 Dead Stock (Critical)'

            stock_health_analysis = df_calc.groupby('Stock_Health').agg({
                'Closing Amount': 'sum',
                'Item Code': 'count'
            }).reset_index()

            if not stock_health_analysis.empty:
                stock_health_info_text = """
                🏥 Stock Health Check helps identify inventory performance issues:

                🟢 Healthy Stock (Turnover >2x):
                • Fast-moving items with good demand
                • Ensure adequate supply to avoid stockouts
                • These items generate good cash flow

                🟡 Moderate Stock (Turnover 1-2x):
                • Average performance, monitor closely
                • Consider promotional activities to boost movement
                • Review demand patterns and seasonality

                🟠 Slow Stock (Turnover <1x):
                • Slow-moving inventory tying up cash
                • Investigate reasons: pricing, demand, competition
                • Consider markdowns or bundle offers

                🔴 Dead Stock (No Movement):
                • Critical issue - inventory not moving at all
                • Immediate action required: liquidate, return, or write-off
                • Review procurement processes to prevent future dead stock
                """

                charts.append({
                    "id": "stock_health_analysis",
                    "title": "Stock Health Check: Identify Performance Issues Early",
                    "subtitle": "Categorize your inventory by movement speed to take targeted actions",
                    "info": stock_health_info_text,
                    "type": "doughnut",
                    "data": {
                        "labels": stock_health_analysis['Stock_Health'].tolist(),
                        "datasets": [{
                            "label": "Inventory Value (₹)",
                            "data": [float(val) for val in stock_health_analysis['Closing Amount'].round(2).tolist()],
                            "backgroundColor": ['#27AE60', '#F39C12', '#E67E22', '#E74C3C'],
                            "borderColor": '#FFFFFF',
                            "borderWidth": 3,
                            "hoverBorderWidth": 4
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "cutout": '50%',
                        "plugins": {
                            "legend": {
                                "position": "bottom",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 15,
                                    "font": {"size": 11},
                                    "boxWidth": 12
                                }
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return context.label; }",
                                    "label": "function(context) { var total = context.dataset.data.reduce((a, b) => a + b, 0); var percentage = ((context.parsed / total) * 100).toFixed(1); var items = [" + str(stock_health_analysis['Item Code'].tolist()) + "][context.dataIndex]; return 'Value: ₹' + context.parsed.toLocaleString() + ' (' + percentage + '% of total) | Items: ' + items; }",
                                    "afterLabel": "function(context) { var actions = ['Monitor supply chain', 'Review demand patterns', 'Consider promotions', 'Immediate action required']; return 'Action: ' + actions[context.dataIndex]; }"
                                }
                            }
                        }
                    }
                })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating inventory dashboard: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def smart_ask_dashboard(df: pd.DataFrame, user_query: str = "", use_default_charts: bool = True) -> dict:
    openai_api_key = os.getenv("OPENAI_API_KEY")
    llm = OpenAI(api_key=openai_api_key, model="gpt-4")

    # Get global chart colors for consistency
    global_colors = get_global_chart_colors()
    colors_str = str(global_colors)

    columns = list(df.columns)
    
    if use_default_charts or not user_query:
        analysis_prompt = f"""
            You are a data analyst generating a dashboard for an Indian restaurant business.

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            
            - **Purchase Overview Section**: A dedicated section showcasing purchase metrics.
            - **Total Purchase Cost**: Bar chart displaying the total cost of purchases.
            - **Purchase Cost by Location**, Supplier: Grouped bar chart or stacked bar chart illustrating spending patterns.
            - **Cost Trends**: Line chart or area chart depicting cost trends over time.
            - **Overall Purchase Value Comparison**: Bar chart or bullet graph for holistic spending analysis.
            - **Cost per Item**: Line chart or bar chart showing the average cost per item.
            - **Purchase Order Accuracy**: Area chart or line chart indicating the accuracy of purchase orders.
            - **Purchase by Category**: Stacked bar chart or table showcasing purchase distribution by categories.
            - **Top 15 Purchase Items**: Table or bar chart displaying the top 15 purchased items.

            Rules:
            - Use only actual data values.
            - For date-wise insights, use columns containing 'Based on'.
            - No assumptions or fabrication.

            Your task:
            - Extract factual business metrics.
            - Identify trends, totals, comparisons.
            - Keep insights concise.
        """
    else:
        analysis_prompt = f"""
            You are a data analyst answering this business question for an Indian restaurant:

            "{user_query}"

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            Rules:
            - Use only actual data.
            - Use only date columns containing 'Based on'.
            - No guessing or fabrication.
            - Be concise with numeric summaries or patterns.
        """
    
    query_engine = PandasQueryEngine(df=df, llm=llm, verbose=False, synthesize_response=True)
    analysis_response = query_engine.query(analysis_prompt)
    analysis_text = str(analysis_response).strip()

    print("Analysis text:", analysis_text)

    formatting_prompt = f"""
        Convert the following analysis text into a single valid JSON matching this schema:

        {{
        "charts": [
            {{
            "id": "string",
            "title": "string",
            "type": "string",
            "data": {{
                "labels": ["string"],
                "datasets": [
                {{
                    "label": "string",
                    "data": [float],
                    "backgroundColor": ["string"],
                    "borderColor": ["string"]
                }}
                ]
            }}
            }}
        ],
        "summary_items": [
            {{
            "icon": "string",
            "value": "string",
            "label": "string",
            "data_type": "string"
            }}
        ]
        }}

        Use only facts from the text below — no extra info or calculations.
        Match label and data array lengths exactly.
        Use these colors in order for charts: {colors_str}
        Output ONLY JSON — no explanations or text.

        IMPORTANT:
        - Summary items should be concise top-level business KPIs.
        - Do NOT repeat any data already shown in charts within summary items.

        Analysis text:
        \"\"\"
        {analysis_text}
        \"\"\"
    """

    json_response = llm.complete(prompt=formatting_prompt).text.strip()
    dashboard = DashboardResponse.parse_raw(json_response)

    return {
        "success": True,
        "charts": dashboard.charts,
        "summary_items": dashboard.summary_items
    }
