{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/share-data.service\";\nimport * as i4 from \"../../services/dashboard-config.service\";\nimport * as i5 from \"../../services/chart-renderer.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/datepicker\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"ng2-charts\";\nimport * as i17 from \"ngx-mat-select-search\";\nimport * as i18 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 38);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SmartDashboardComponent_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dashboardType_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dashboardType_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dashboardType_r10.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r11.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", branch_r11.branchName, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 38);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SmartDashboardComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDateOption_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDateOption_r12.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDateOption_r12.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"mat-spinner\", 41);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_86_div_1_div_18_mat_card_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 63)(1, \"mat-card-content\")(2, \"div\", 64)(3, \"div\", 65)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 66)(7, \"div\", 67);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 68);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r19 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r19.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r19.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r19.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r19.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r19.label);\n  }\n}\nfunction SmartDashboardComponent_div_86_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_86_div_1_div_18_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const group_r17 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.toggleGroupExpansion(group_r17.id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 55)(3, \"mat-icon\", 56);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 58);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-icon\", 59);\n    i0.ɵɵtext(10, \" expand_more \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 60)(12, \"div\", 61);\n    i0.ɵɵtemplate(13, SmartDashboardComponent_div_86_div_1_div_18_mat_card_13_Template, 11, 7, \"mat-card\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const group_r17 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(group_r17.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(group_r17.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(group_r17.card_count);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"expanded\", group_r17.is_expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"expanded\", group_r17.is_expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", group_r17.cards);\n  }\n}\nfunction SmartDashboardComponent_div_86_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"span\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_86_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.expandAllGroups());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"expand_more\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Expand All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_86_div_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.collapseAllGroups());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"expand_less\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Collapse All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_86_div_1_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.toggleGroupedView());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"view_list\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Simple View \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, SmartDashboardComponent_div_86_div_1_div_18_Template, 14, 8, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r13.cardGroupsData == null ? null : ctx_r13.cardGroupsData.total_cards, \" metrics organized in \", ctx_r13.cardGroups.length, \" groups\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.cardGroups);\n  }\n}\nfunction SmartDashboardComponent_div_86_div_2_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_86_div_2_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.toggleGroupedView());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"view_module\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Grouped View \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SmartDashboardComponent_div_86_div_2_mat_card_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 75)(1, \"mat-card-content\")(2, \"div\", 64)(3, \"div\", 65)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 66)(7, \"div\", 67);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 68);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r30 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r30.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r30.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r30.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r30.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r30.label);\n  }\n}\nfunction SmartDashboardComponent_div_86_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"span\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SmartDashboardComponent_div_86_div_2_button_4_Template, 4, 0, \"button\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 73);\n    i0.ɵɵtemplate(6, SmartDashboardComponent_div_86_div_2_mat_card_6_Template, 11, 7, \"mat-card\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.summaryCards.length, \" Key Metrics\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.cardGroups.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.summaryCards);\n  }\n}\nfunction SmartDashboardComponent_div_86_div_3_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 78)(1, \"mat-card-header\")(2, \"mat-card-title\", 79);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 80);\n    i0.ɵɵelement(6, \"canvas\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chart_r32 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r31.getChartCssClass(chart_r32));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chart_r32.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-chart-type\", chart_r32.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"type\", ctx_r31.getChartType(chart_r32))(\"data\", ctx_r31.getChartData(chart_r32))(\"options\", ctx_r31.getChartOptions(chart_r32));\n  }\n}\nfunction SmartDashboardComponent_div_86_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_86_div_3_mat_card_1_Template, 7, 6, \"mat-card\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.charts);\n  }\n}\nfunction SmartDashboardComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_86_div_1_Template, 19, 3, \"div\", 43);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_86_div_2_Template, 7, 3, \"div\", 44);\n    i0.ɵɵtemplate(3, SmartDashboardComponent_div_86_div_3_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.useGroupedCards && ctx_r8.cardGroups.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.useGroupedCards && ctx_r8.summaryCards.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.charts.length > 0);\n  }\n}\nfunction SmartDashboardComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"mat-icon\", 83);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please select a location and date range to view dashboard data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_87_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.loadDashboardData());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, configService, chartRenderer, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.configService = configService;\n    this.chartRenderer = chartRenderer;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocation = null;\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl();\n    this.endDate = new FormControl();\n    this.searchQuery = new FormControl('');\n    this.baseDateCtrl = new FormControl();\n    this.selectedDashboard = '';\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    this.isConfigLoaded = false;\n    // Grouped cards data\n    this.cardGroups = [];\n    this.cardGroupsData = null;\n    this.useGroupedCards = false;\n    // Dynamic configuration data\n    this.dashboardTypes = [];\n    this.baseDateOptions = [];\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n  initializeConfig() {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        } else {\n          this.setupDefaultConfigurations();\n        }\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  setupDynamicConfigurations(config) {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n    const defaultDateRangeDays = uiConfig.default_date_range_days || 30;\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - defaultDateRangeDays * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n    // Load dashboard data after configuration is set\n    setTimeout(() => {\n      this.loadDashboardData();\n    }, 100);\n  }\n  setupDefaultConfigurations() {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [{\n      value: 'purchase',\n      label: 'Purchase Dashboard'\n    }, {\n      value: 'inventory',\n      label: 'Inventory Dashboard'\n    }, {\n      value: 'sales',\n      label: 'Sales Dashboard'\n    }];\n    this.baseDateOptions = [{\n      value: 'deliveryDate',\n      label: 'Delivery Date'\n    }, {\n      value: 'orderDate',\n      label: 'Order Date'\n    }, {\n      value: 'createdDate',\n      label: 'Created Date'\n    }];\n    this.selectedDashboard = 'purchase';\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n    // Load dashboard data after fallback configuration is set\n    setTimeout(() => {\n      this.loadDashboardData();\n    }, 100);\n  }\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    // Don't load dashboard data immediately - wait for config to load\n    // Dashboard data will be loaded after config is ready\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      if (this.branches.length === 1) {\n        this.selectedLocation = this.branches[0].restaurantIdOld;\n      }\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  clearDashboardData() {\n    this.summaryCards = [];\n    this.charts = [];\n    this.cardGroups = [];\n    this.cardGroupsData = null;\n    this.useGroupedCards = false;\n  }\n  processDashboardData(data) {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n    // Process grouped cards if available\n    if (data.card_groups && data.card_groups.grouping_enabled) {\n      this.cardGroupsData = data.card_groups;\n      this.cardGroups = data.card_groups.groups?.map(group => ({\n        ...group,\n        cards: group.cards?.map(item => ({\n          icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n          value: item.value,\n          label: item.label,\n          color: this.configService.getSummaryCardColor(item.data_type),\n          data_type: item.data_type\n        })) || []\n      })) || [];\n      this.useGroupedCards = this.cardGroups.length > 0;\n    } else {\n      this.cardGroups = [];\n      this.cardGroupsData = null;\n      this.useGroupedCards = false;\n    }\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map(chart => this.chartRenderer.processChart(chart)) || [];\n  }\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  onLocationChange() {\n    this.loadDashboardData();\n  }\n  onDateChange() {\n    this.loadDashboardData();\n  }\n  onDashboardChange() {\n    this.loadDashboardData();\n  }\n  onSearchQuery() {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n  loadDashboardDataWithQuery(query) {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Dynamic chart methods using services\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n  getChartOptions(chart) {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n  getChartCssClass(chart) {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n  // Grouped cards methods\n  toggleGroupExpansion(groupId) {\n    const group = this.cardGroups.find(g => g.id === groupId);\n    if (group) {\n      group.is_expanded = !group.is_expanded;\n    }\n  }\n  expandAllGroups() {\n    this.cardGroups.forEach(group => {\n      group.is_expanded = true;\n    });\n  }\n  collapseAllGroups() {\n    this.cardGroups.forEach(group => {\n      group.is_expanded = false;\n    });\n  }\n  toggleGroupedView() {\n    this.useGroupedCards = !this.useGroupedCards;\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i4.DashboardConfigService), i0.ɵɵdirectiveInject(i5.ChartRendererService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 88,\n      vars: 21,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"main-layout\"], [1, \"left-sidebar\"], [1, \"dashboard-selection\"], [\"appearance\", \"outline\", 1, \"dashboard-dropdown\"], [3, \"value\", \"disabled\", \"valueChange\", \"selectionChange\"], [\"disabled\", \"\", 4, \"ngIf\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"filters-section\"], [1, \"filters-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"value\", \"valueChange\", \"selectionChange\"], [\"placeholderLabel\", \"Search locations...\", \"noEntriesFoundLabel\", \"No locations found\", 3, \"formControl\"], [3, \"formControl\", \"disabled\", \"selectionChange\"], [\"matInput\", \"\", 3, \"matDatepicker\", \"formControl\", \"dateChange\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"endPicker\", \"\"], [1, \"filter-actions\"], [\"mat-stroked-button\", \"\", 1, \"reset-filters-btn\", 3, \"click\"], [1, \"right-content\"], [1, \"search-header\"], [1, \"assistant-info\"], [1, \"assistant-icon\"], [1, \"assistant-text\"], [1, \"assistant-title\"], [1, \"assistant-status\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask me about your business data\", 3, \"formControl\", \"keyup.enter\"], [\"matSuffix\", \"\", 1, \"search-icon\", 3, \"click\"], [1, \"dashboard-content-area\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"disabled\", \"\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-grid\"], [\"class\", \"grouped-cards-section\", 4, \"ngIf\"], [\"class\", \"summary-cards-row\", 4, \"ngIf\"], [\"class\", \"charts-grid\", 4, \"ngIf\"], [1, \"grouped-cards-section\"], [1, \"group-controls\"], [1, \"group-info\"], [1, \"group-count\"], [1, \"group-actions\"], [\"mat-button\", \"\", 1, \"group-action-btn\", 3, \"click\"], [\"class\", \"card-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"card-group\"], [1, \"group-header\", 3, \"click\"], [1, \"group-title\"], [1, \"group-icon\"], [1, \"group-name\"], [1, \"group-badge\"], [1, \"expand-icon\"], [1, \"group-content\"], [1, \"group-cards-grid\"], [\"class\", \"summary-card grouped-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\", \"grouped-card\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"summary-cards-row\"], [1, \"cards-header\"], [1, \"cards-count\"], [\"mat-button\", \"\", \"class\", \"group-action-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"cards-grid\"], [\"class\", \"summary-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"charts-grid\"], [\"class\", \"chart-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-card\", 3, \"ngClass\"], [1, \"chart-title\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-form-field\", 4)(5, \"mat-label\");\n          i0.ɵɵtext(6, \"Select Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-select\", 5);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_7_listener($event) {\n            return ctx.selectedDashboard = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_7_listener() {\n            return ctx.onDashboardChange();\n          });\n          i0.ɵɵtemplate(8, SmartDashboardComponent_mat_option_8_Template, 2, 0, \"mat-option\", 6);\n          i0.ɵɵtemplate(9, SmartDashboardComponent_mat_option_9_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"h3\", 9)(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Smart Filters \");\n          i0.ɵɵelementStart(15, \"span\", 10);\n          i0.ɵɵtext(16, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"h4\", 12)(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 13)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Select restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-select\", 14);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_25_listener($event) {\n            return ctx.selectedLocation = $event;\n          })(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_25_listener() {\n            return ctx.onLocationChange();\n          });\n          i0.ɵɵelementStart(26, \"mat-option\");\n          i0.ɵɵelement(27, \"ngx-mat-select-search\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, SmartDashboardComponent_mat_option_28_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 11)(30, \"h4\", 12)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"event\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" Base Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"mat-form-field\", 13)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Select base date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"mat-select\", 16);\n          i0.ɵɵlistener(\"selectionChange\", function SmartDashboardComponent_Template_mat_select_selectionChange_37_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵtemplate(38, SmartDashboardComponent_mat_option_38_Template, 2, 0, \"mat-option\", 6);\n          i0.ɵɵtemplate(39, SmartDashboardComponent_mat_option_39_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 11)(41, \"h4\", 12)(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-form-field\", 13)(46, \"mat-label\");\n          i0.ɵɵtext(47, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"input\", 17);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_48_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"mat-datepicker-toggle\", 18)(50, \"mat-datepicker\", null, 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 11)(53, \"h4\", 12)(54, \"mat-icon\");\n          i0.ɵɵtext(55, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(56, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"mat-form-field\", 13)(58, \"mat-label\");\n          i0.ɵɵtext(59, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"input\", 17);\n          i0.ɵɵlistener(\"dateChange\", function SmartDashboardComponent_Template_input_dateChange_60_listener() {\n            return ctx.onDateChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(61, \"mat-datepicker-toggle\", 18)(62, \"mat-datepicker\", null, 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"div\", 21)(65, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_65_listener() {\n            return ctx.loadDashboardData();\n          });\n          i0.ɵɵelementStart(66, \"mat-icon\");\n          i0.ɵɵtext(67, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(68, \" Reset filters \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(69, \"div\", 23)(70, \"div\", 24)(71, \"div\", 25)(72, \"mat-icon\", 26);\n          i0.ɵɵtext(73, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 27)(75, \"span\", 28);\n          i0.ɵɵtext(76, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"span\", 29);\n          i0.ɵɵtext(78, \"Ready to analyze\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\", 30)(80, \"mat-form-field\", 31)(81, \"input\", 32);\n          i0.ɵɵlistener(\"keyup.enter\", function SmartDashboardComponent_Template_input_keyup_enter_81_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"mat-icon\", 33);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_mat_icon_click_82_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵtext(83, \"search\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(84, \"div\", 34);\n          i0.ɵɵtemplate(85, SmartDashboardComponent_div_85_Template, 4, 0, \"div\", 35);\n          i0.ɵɵtemplate(86, SmartDashboardComponent_div_86_Template, 4, 3, \"div\", 36);\n          i0.ɵɵtemplate(87, SmartDashboardComponent_div_87_Template, 11, 0, \"div\", 37);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r5 = i0.ɵɵreference(51);\n          const _r6 = i0.ɵɵreference(63);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.selectedDashboard)(\"disabled\", !ctx.isConfigLoaded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isConfigLoaded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.dashboardTypes);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formControl\", ctx.baseDateCtrl)(\"disabled\", !ctx.isConfigLoaded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isConfigLoaded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baseDateOptions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"formControl\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"matDatepicker\", _r6)(\"formControl\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r6);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"formControl\", ctx.searchQuery);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.summaryCards.length > 0 || ctx.charts.length > 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.summaryCards.length === 0 && ctx.charts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, MatCardModule, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatButtonModule, i8.MatButton, MatIconModule, i9.MatIcon, MatSelectModule, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatSelect, i12.MatOption, MatFormFieldModule, MatInputModule, i13.MatInput, MatDatepickerModule, i14.MatDatepicker, i14.MatDatepickerInput, i14.MatDatepickerToggle, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, i15.MatProgressSpinner, NgChartsModule, i16.BaseChartDirective, NgxMatSelectSearchModule, i17.MatSelectSearchComponent, ReactiveFormsModule, i18.DefaultValueAccessor, i18.NgControlStatus, i18.FormControlDirective, FormsModule],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 571:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nunmatched \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n898 \\u2502 }\\\\n    \\u2502 ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\smart-dashboard\\\\\\\\smart-dashboard.component.scss 898:1  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[571]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "dashboardType_r10", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "branch_r11", "restaurantIdOld", "branchName", "baseDateOption_r12", "ɵɵelement", "ɵɵstyleProp", "card_r19", "color", "ɵɵtextInterpolate", "icon", "ɵɵlistener", "SmartDashboardComponent_div_86_div_1_div_18_Template_div_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r21", "group_r17", "$implicit", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "toggleGroupExpansion", "id", "ɵɵtemplate", "SmartDashboardComponent_div_86_div_1_div_18_mat_card_13_Template", "title", "card_count", "ɵɵclassProp", "is_expanded", "cards", "SmartDashboardComponent_div_86_div_1_Template_button_click_6_listener", "_r23", "ctx_r22", "expandAllGroups", "SmartDashboardComponent_div_86_div_1_Template_button_click_10_listener", "ctx_r24", "collapseAllGroups", "SmartDashboardComponent_div_86_div_1_Template_button_click_14_listener", "ctx_r25", "toggleGroupedView", "SmartDashboardComponent_div_86_div_1_div_18_Template", "ɵɵtextInterpolate2", "ctx_r13", "cardGroupsData", "total_cards", "cardGroups", "length", "SmartDashboardComponent_div_86_div_2_button_4_Template_button_click_0_listener", "_r29", "ctx_r28", "card_r30", "SmartDashboardComponent_div_86_div_2_button_4_Template", "SmartDashboardComponent_div_86_div_2_mat_card_6_Template", "ctx_r14", "summaryCards", "ctx_r31", "getChartCssClass", "chart_r32", "ɵɵattribute", "type", "getChartType", "getChartData", "getChartOptions", "SmartDashboardComponent_div_86_div_3_mat_card_1_Template", "ctx_r15", "charts", "SmartDashboardComponent_div_86_div_1_Template", "SmartDashboardComponent_div_86_div_2_Template", "SmartDashboardComponent_div_86_div_3_Template", "ctx_r8", "useGroupedCards", "SmartDashboardComponent_div_87_Template_button_click_7_listener", "_r35", "ctx_r34", "loadDashboardData", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "configService", "<PERSON><PERSON><PERSON><PERSON>", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocation", "locationFilterCtrl", "startDate", "endDate", "searchQuery", "baseDateCtrl", "selectedDashboard", "isLoading", "isConfigLoaded", "dashboardTypes", "baseDateOptions", "user", "getCurrentUser", "initializeConfig", "loadConfig", "subscribe", "next", "response", "status", "setConfig", "data", "setupDynamicConfigurations", "setupDefaultConfigurations", "detectChanges", "error", "config", "dashboard_types", "base_date_options", "uiConfig", "ui_config", "defaultDateRangeDays", "default_date_range_days", "default_dashboard_type", "setValue", "default_base_date", "Date", "now", "setTimeout", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "complete", "valueChanges", "pipe", "filterBranches", "selectedBranchesSource", "searchTerm", "normalizedSearchTerm", "toLowerCase", "replace", "filter", "branch", "includes", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "dashboard_type", "getSmartDashboardData", "processDashboardData", "clearDashboardData", "summary_items", "map", "item", "getSummaryCardIcon", "data_type", "getSummaryCardColor", "card_groups", "grouping_enabled", "groups", "group", "chart", "processChart", "date", "toISOString", "split", "onLocationChange", "onDateChange", "onDashboardChange", "onSearchQuery", "query", "trim", "loadDashboardDataWithQuery", "options", "getDefaultChartOptions", "groupId", "find", "g", "for<PERSON>ach", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "ShareDataService", "i4", "DashboardConfigService", "i5", "ChartRendererService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_7_listener", "$event", "SmartDashboardComponent_Template_mat_select_selectionChange_7_listener", "SmartDashboardComponent_mat_option_8_Template", "SmartDashboardComponent_mat_option_9_Template", "SmartDashboardComponent_Template_mat_select_valueChange_25_listener", "SmartDashboardComponent_Template_mat_select_selectionChange_25_listener", "SmartDashboardComponent_mat_option_28_Template", "SmartDashboardComponent_Template_mat_select_selectionChange_37_listener", "SmartDashboardComponent_mat_option_38_Template", "SmartDashboardComponent_mat_option_39_Template", "SmartDashboardComponent_Template_input_dateChange_48_listener", "SmartDashboardComponent_Template_input_dateChange_60_listener", "SmartDashboardComponent_Template_button_click_65_listener", "SmartDashboardComponent_Template_input_keyup_enter_81_listener", "SmartDashboardComponent_Template_mat_icon_click_82_listener", "SmartDashboardComponent_div_85_Template", "SmartDashboardComponent_div_86_Template", "SmartDashboardComponent_div_87_Template", "_r5", "_r6", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i8", "MatButton", "i9", "MatIcon", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatSelect", "i12", "MatOption", "i13", "MatInput", "i14", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i15", "MatProgressSpinner", "i16", "BaseChartDirective", "i17", "MatSelectSearchComponent", "i18", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.html"], "sourcesContent": ["import { Compo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService, CardGroup, CardGroupsData } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\nimport { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';\nimport { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n  data_type?: string;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocation: string | null = null;\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl();\n  endDate = new FormControl();\n  searchQuery = new FormControl('');\n  baseDateCtrl = new FormControl();\n  selectedDashboard = '';\n\n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartModel[] = [];\n  isLoading = false;\n  isConfigLoaded = false;\n\n  // Grouped cards data\n  cardGroups: CardGroup[] = [];\n  cardGroupsData: CardGroupsData | null = null;\n  useGroupedCards = false;\n\n  // Dynamic configuration data\n  dashboardTypes: DashboardType[] = [];\n  baseDateOptions: BaseDateOption[] = [];\n\n\n\n\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private configService: DashboardConfigService,\n    private chartRenderer: ChartRendererService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n\n  private initializeConfig(): void {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        } else {\n          this.setupDefaultConfigurations();\n        }\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n\n  private setupDynamicConfigurations(config: any): void {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n    const defaultDateRangeDays = uiConfig.default_date_range_days || 30;\n\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - defaultDateRangeDays * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n\n    // Load dashboard data after configuration is set\n    setTimeout(() => {\n      this.loadDashboardData();\n    }, 100);\n  }\n\n  private setupDefaultConfigurations(): void {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [\n      { value: 'purchase', label: 'Purchase Dashboard' },\n      { value: 'inventory', label: 'Inventory Dashboard' },\n      { value: 'sales', label: 'Sales Dashboard' }\n    ];\n    this.baseDateOptions = [\n      { value: 'deliveryDate', label: 'Delivery Date' },\n      { value: 'orderDate', label: 'Order Date' },\n      { value: 'createdDate', label: 'Created Date' }\n    ];\n    this.selectedDashboard = 'purchase';\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.startDate.setValue(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));\n    this.endDate.setValue(new Date());\n\n    // Load dashboard data after fallback configuration is set\n    setTimeout(() => {\n      this.loadDashboardData();\n    }, 100);\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    // Don't load dashboard data immediately - wait for config to load\n    // Dashboard data will be loaded after config is ready\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n        \n        if (this.branches.length === 1) {\n          this.selectedLocation = this.branches[0].restaurantIdOld;\n        }\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private clearDashboardData(): void {\n    this.summaryCards = [];\n    this.charts = [];\n    this.cardGroups = [];\n    this.cardGroupsData = null;\n    this.useGroupedCards = false;\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n\n    // Process grouped cards if available\n    if (data.card_groups && data.card_groups.grouping_enabled) {\n      this.cardGroupsData = data.card_groups;\n      this.cardGroups = data.card_groups.groups?.map((group: any) => ({\n        ...group,\n        cards: group.cards?.map((item: any) => ({\n          icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n          value: item.value,\n          label: item.label,\n          color: this.configService.getSummaryCardColor(item.data_type),\n          data_type: item.data_type\n        })) || []\n      })) || [];\n      this.useGroupedCards = this.cardGroups.length > 0;\n    } else {\n      this.cardGroups = [];\n      this.cardGroupsData = null;\n      this.useGroupedCards = false;\n    }\n\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map((chart: any) =>\n      this.chartRenderer.processChart(chart)\n    ) || [];\n  }\n\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  onLocationChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDateChange(): void {\n    this.loadDashboardData();\n  }\n\n  onDashboardChange(): void {\n    this.loadDashboardData();\n  }\n\n  onSearchQuery(): void {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n\n  private loadDashboardDataWithQuery(query: string): void {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocation ? [this.selectedLocation] : [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  // Dynamic chart methods using services\n  getChartData(chart: ChartModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartModel): ChartType {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n\n  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n\n  getChartCssClass(chart: ChartModel): string {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n\n  // Grouped cards methods\n  toggleGroupExpansion(groupId: string): void {\n    const group = this.cardGroups.find(g => g.id === groupId);\n    if (group) {\n      group.is_expanded = !group.is_expanded;\n    }\n  }\n\n  expandAllGroups(): void {\n    this.cardGroups.forEach(group => {\n      group.is_expanded = true;\n    });\n  }\n\n  collapseAllGroups(): void {\n    this.cardGroups.forEach(group => {\n      group.is_expanded = false;\n    });\n  }\n\n  toggleGroupedView(): void {\n    this.useGroupedCards = !this.useGroupedCards;\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Main Layout -->\n  <div class=\"main-layout\">\n    <!-- Left Sidebar -->\n    <div class=\"left-sidebar\">\n      <!-- Dashboard Selection -->\n      <div class=\"dashboard-selection\">\n        <mat-form-field appearance=\"outline\" class=\"dashboard-dropdown\">\n          <mat-label>Select Dashboard</mat-label>\n          <mat-select [(value)]=\"selectedDashboard\" (selectionChange)=\"onDashboardChange()\" [disabled]=\"!isConfigLoaded\">\n            <mat-option *ngIf=\"!isConfigLoaded\" disabled>Loading...</mat-option>\n            <mat-option *ngFor=\"let dashboardType of dashboardTypes\" [value]=\"dashboardType.value\">\n              {{dashboardType.label}}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n      <!-- Smart Filters Section -->\n      <div class=\"filters-section\">\n        <h3 class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          Smart Filters\n          <span class=\"filter-count\">1</span>\n        </h3>\n\n        <!-- Restaurants Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>restaurant</mat-icon>\n            Restaurants\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select restaurants</mat-label>\n            <mat-select [(value)]=\"selectedLocation\" (selectionChange)=\"onLocationChange()\">\n              <mat-option>\n                <ngx-mat-select-search\n                  [formControl]=\"locationFilterCtrl\"\n                  placeholderLabel=\"Search locations...\"\n                  noEntriesFoundLabel=\"No locations found\">\n                </ngx-mat-select-search>\n              </mat-option>\n              <mat-option *ngFor=\"let branch of filteredBranches\" [value]=\"branch.restaurantIdOld\">\n                {{branch.branchName}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select base date</mat-label>\n            <mat-select [formControl]=\"baseDateCtrl\" (selectionChange)=\"onDateChange()\" [disabled]=\"!isConfigLoaded\">\n              <mat-option *ngIf=\"!isConfigLoaded\" disabled>Loading...</mat-option>\n              <mat-option *ngFor=\"let baseDateOption of baseDateOptions\" [value]=\"baseDateOption.value\">\n                {{baseDateOption.label}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Start Date</mat-label>\n            <input matInput [matDatepicker]=\"startPicker\" [formControl]=\"startDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>End Date</mat-label>\n            <input matInput [matDatepicker]=\"endPicker\" [formControl]=\"endDate\" (dateChange)=\"onDateChange()\">\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- Reset Filters Button -->\n        <div class=\"filter-actions\">\n          <button mat-stroked-button class=\"reset-filters-btn\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Reset filters\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Right Content Area -->\n    <div class=\"right-content\">\n      <!-- Top Search Bar -->\n      <div class=\"search-header\">\n        <div class=\"assistant-info\">\n          <mat-icon class=\"assistant-icon\">smart_toy</mat-icon>\n          <div class=\"assistant-text\">\n            <span class=\"assistant-title\">Smart Dashboard Assistant</span>\n            <span class=\"assistant-status\">Ready to analyze</span>\n          </div>\n        </div>\n        <div class=\"search-container\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <input matInput\n                   placeholder=\"Ask me about your business data\"\n                   [formControl]=\"searchQuery\"\n                   (keyup.enter)=\"onSearchQuery()\" />\n            <mat-icon matSuffix class=\"search-icon\" (click)=\"onSearchQuery()\">search</mat-icon>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Dashboard Content Area -->\n      <div class=\"dashboard-content-area\">\n        <!-- Loading Spinner -->\n        <div *ngIf=\"isLoading\" class=\"loading-container\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading dashboard data...</p>\n        </div>\n\n        <!-- Dashboard Grid -->\n        <div *ngIf=\"!isLoading && (summaryCards.length > 0 || charts.length > 0)\" class=\"dashboard-grid\">\n\n          <!-- Grouped Cards Section -->\n          <div *ngIf=\"useGroupedCards && cardGroups.length > 0\" class=\"grouped-cards-section\">\n            <!-- Group Controls -->\n            <div class=\"group-controls\">\n              <div class=\"group-info\">\n                <span class=\"group-count\">{{cardGroupsData?.total_cards}} metrics organized in {{cardGroups.length}} groups</span>\n              </div>\n              <div class=\"group-actions\">\n                <button mat-button (click)=\"expandAllGroups()\" class=\"group-action-btn\">\n                  <mat-icon>expand_more</mat-icon>\n                  Expand All\n                </button>\n                <button mat-button (click)=\"collapseAllGroups()\" class=\"group-action-btn\">\n                  <mat-icon>expand_less</mat-icon>\n                  Collapse All\n                </button>\n                <button mat-button (click)=\"toggleGroupedView()\" class=\"group-action-btn\">\n                  <mat-icon>view_list</mat-icon>\n                  Simple View\n                </button>\n              </div>\n            </div>\n\n            <!-- Card Groups -->\n            <div *ngFor=\"let group of cardGroups\" class=\"card-group\">\n              <div class=\"group-header\" (click)=\"toggleGroupExpansion(group.id)\">\n                <div class=\"group-title\">\n                  <mat-icon class=\"group-icon\">{{group.icon}}</mat-icon>\n                  <span class=\"group-name\">{{group.title}}</span>\n                  <span class=\"group-badge\">{{group.card_count}}</span>\n                </div>\n                <mat-icon class=\"expand-icon\" [class.expanded]=\"group.is_expanded\">\n                  expand_more\n                </mat-icon>\n              </div>\n\n              <div class=\"group-content\" [class.expanded]=\"group.is_expanded\">\n                <div class=\"group-cards-grid\">\n                  <mat-card *ngFor=\"let card of group.cards\" class=\"summary-card grouped-card\" [style.border-left-color]=\"card.color\">\n                    <mat-card-content>\n                      <div class=\"card-content\">\n                        <div class=\"card-icon\" [style.color]=\"card.color\">\n                          <mat-icon>{{card.icon}}</mat-icon>\n                        </div>\n                        <div class=\"card-info\">\n                          <div class=\"card-value\">{{card.value}}</div>\n                          <div class=\"card-label\">{{card.label}}</div>\n                        </div>\n                      </div>\n                    </mat-card-content>\n                  </mat-card>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Traditional Summary Cards Row (fallback) -->\n          <div *ngIf=\"!useGroupedCards && summaryCards.length > 0\" class=\"summary-cards-row\">\n            <div class=\"cards-header\">\n              <span class=\"cards-count\">{{summaryCards.length}} Key Metrics</span>\n              <button *ngIf=\"cardGroups.length > 0\" mat-button (click)=\"toggleGroupedView()\" class=\"group-action-btn\">\n                <mat-icon>view_module</mat-icon>\n                Grouped View\n              </button>\n            </div>\n            <div class=\"cards-grid\">\n              <mat-card *ngFor=\"let card of summaryCards\" class=\"summary-card\" [style.border-left-color]=\"card.color\">\n                <mat-card-content>\n                  <div class=\"card-content\">\n                    <div class=\"card-icon\" [style.color]=\"card.color\">\n                      <mat-icon>{{card.icon}}</mat-icon>\n                    </div>\n                    <div class=\"card-info\">\n                      <div class=\"card-value\">{{card.value}}</div>\n                      <div class=\"card-label\">{{card.label}}</div>\n                    </div>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n\n          <!-- Charts Grid -->\n          <div *ngIf=\"charts.length > 0\" class=\"charts-grid\">\n            <mat-card *ngFor=\"let chart of charts; let i = index\"\n                      class=\"chart-card\"\n                      [ngClass]=\"getChartCssClass(chart)\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">{{chart.title}}</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\" [attr.data-chart-type]=\"chart.type\">\n                  <!-- Dynamic Chart Rendering -->\n                  <canvas baseChart\n                          [type]=\"getChartType(chart)\"\n                          [data]=\"getChartData(chart)\"\n                          [options]=\"getChartOptions(chart)\">\n                  </canvas>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- Single Unified Empty State -->\n        <div *ngIf=\"!isLoading && summaryCards.length === 0 && charts.length === 0\" class=\"empty-state\">\n          <mat-icon class=\"empty-icon\">analytics</mat-icon>\n          <h3>No Data Available</h3>\n          <p>Please select a location and date range to view dashboard data.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"loadDashboardData()\">\n            <mat-icon>refresh</mat-icon>\n            Refresh Data\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;ICNjEC,EAAA,CAAAC,cAAA,qBAA6C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IACpEH,EAAA,CAAAC,cAAA,qBAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,iBAAA,CAAAC,KAAA,CAA6B;IACpFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,iBAAA,CAAAI,KAAA,MACF;;;;;IA4BET,EAAA,CAAAC,cAAA,qBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAM,UAAA,CAAAC,eAAA,CAAgC;IAClFX,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,UAAA,CAAAE,UAAA,MACF;;;;;IAcAZ,EAAA,CAAAC,cAAA,qBAA6C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IACpEH,EAAA,CAAAC,cAAA,qBAA0F;IACxFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAS,kBAAA,CAAAP,KAAA,CAA8B;IACvFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAK,kBAAA,CAAAJ,KAAA,MACF;;;;;IAoENT,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAc,SAAA,sBAAyC;IACzCd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA4CxBH,EAAA,CAAAC,cAAA,mBAAoH;IAIlGD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARyBH,EAAA,CAAAe,WAAA,sBAAAC,QAAA,CAAAC,KAAA,CAAsC;IAGtFjB,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAe,WAAA,UAAAC,QAAA,CAAAC,KAAA,CAA0B;IACrCjB,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAkB,iBAAA,CAAAF,QAAA,CAAAG,IAAA,CAAa;IAGCnB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAF,QAAA,CAAAV,KAAA,CAAc;IACdN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAF,QAAA,CAAAP,KAAA,CAAc;;;;;;IAtBpDT,EAAA,CAAAC,cAAA,cAAyD;IAC7BD,EAAA,CAAAoB,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,WAAA,GAAAtB,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAF,OAAA,CAAAG,oBAAA,CAAAL,SAAA,CAAAM,EAAA,CAA8B;IAAA,EAAC;IAChE/B,EAAA,CAAAC,cAAA,cAAyB;IACMD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,mBAAmE;IACjED,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAGbH,EAAA,CAAAC,cAAA,eAAgE;IAE5DD,EAAA,CAAAgC,UAAA,KAAAC,gEAAA,wBAYW;IACbjC,EAAA,CAAAG,YAAA,EAAM;;;;IAxByBH,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAO,SAAA,CAAAN,IAAA,CAAc;IAClBnB,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAkB,iBAAA,CAAAO,SAAA,CAAAS,KAAA,CAAe;IACdlC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAkB,iBAAA,CAAAO,SAAA,CAAAU,UAAA,CAAoB;IAElBnC,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAoC,WAAA,aAAAX,SAAA,CAAAY,WAAA,CAAoC;IAKzCrC,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAoC,WAAA,aAAAX,SAAA,CAAAY,WAAA,CAAoC;IAEhCrC,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,YAAAqB,SAAA,CAAAa,KAAA,CAAc;;;;;;IArCjDtC,EAAA,CAAAC,cAAA,cAAoF;IAIpDD,EAAA,CAAAE,MAAA,GAAiF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpHH,EAAA,CAAAC,cAAA,cAA2B;IACND,EAAA,CAAAoB,UAAA,mBAAAmB,sEAAA;MAAAvC,EAAA,CAAAuB,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAY,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAC5C1C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAAvDD,EAAA,CAAAoB,UAAA,mBAAAuB,uEAAA;MAAA3C,EAAA,CAAAuB,aAAA,CAAAiB,IAAA;MAAA,MAAAI,OAAA,GAAA5C,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAC9C7C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAAvDD,EAAA,CAAAoB,UAAA,mBAAA0B,uEAAA;MAAA9C,EAAA,CAAAuB,aAAA,CAAAiB,IAAA;MAAA,MAAAO,OAAA,GAAA/C,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAkB,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAC9ChD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKbH,EAAA,CAAAgC,UAAA,KAAAiB,oDAAA,mBA6BM;IACRjD,EAAA,CAAAG,YAAA,EAAM;;;;IAjD0BH,EAAA,CAAAO,SAAA,GAAiF;IAAjFP,EAAA,CAAAkD,kBAAA,KAAAC,OAAA,CAAAC,cAAA,kBAAAD,OAAA,CAAAC,cAAA,CAAAC,WAAA,4BAAAF,OAAA,CAAAG,UAAA,CAAAC,MAAA,YAAiF;IAmBxFvD,EAAA,CAAAO,SAAA,IAAa;IAAbP,EAAA,CAAAI,UAAA,YAAA+C,OAAA,CAAAG,UAAA,CAAa;;;;;;IAoClCtD,EAAA,CAAAC,cAAA,iBAAwG;IAAvDD,EAAA,CAAAoB,UAAA,mBAAAoC,+EAAA;MAAAxD,EAAA,CAAAuB,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAA6B,OAAA,CAAAV,iBAAA,EAAmB;IAAA,EAAC;IAC5EhD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAGTH,EAAA,CAAAC,cAAA,mBAAwG;IAItFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARaH,EAAA,CAAAe,WAAA,sBAAA4C,QAAA,CAAA1C,KAAA,CAAsC;IAG1EjB,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAe,WAAA,UAAA4C,QAAA,CAAA1C,KAAA,CAA0B;IACrCjB,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAAxC,IAAA,CAAa;IAGCnB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAArD,KAAA,CAAc;IACdN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAkB,iBAAA,CAAAyC,QAAA,CAAAlD,KAAA,CAAc;;;;;IAjBlDT,EAAA,CAAAC,cAAA,cAAmF;IAErDD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAgC,UAAA,IAAA4B,sDAAA,qBAGS;IACX5D,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAA6B,wDAAA,wBAYW;IACb7D,EAAA,CAAAG,YAAA,EAAM;;;;IApBsBH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,kBAAA,KAAAsD,OAAA,CAAAC,YAAA,CAAAR,MAAA,iBAAmC;IACpDvD,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAA0D,OAAA,CAAAR,UAAA,CAAAC,MAAA,KAA2B;IAMTvD,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,YAAA0D,OAAA,CAAAC,YAAA,CAAe;;;;;IAkB5C/D,EAAA,CAAAC,cAAA,mBAE8C;IAEND,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtEH,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAAc,SAAA,iBAIS;IACXd,EAAA,CAAAG,YAAA,EAAM;;;;;IAZAH,EAAA,CAAAI,UAAA,YAAA4D,OAAA,CAAAC,gBAAA,CAAAC,SAAA,EAAmC;IAELlE,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAkB,iBAAA,CAAAgD,SAAA,CAAAhC,KAAA,CAAe;IAGtBlC,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAmE,WAAA,oBAAAD,SAAA,CAAAE,IAAA,CAAmC;IAGtDpE,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAA4D,OAAA,CAAAK,YAAA,CAAAH,SAAA,EAA4B,SAAAF,OAAA,CAAAM,YAAA,CAAAJ,SAAA,cAAAF,OAAA,CAAAO,eAAA,CAAAL,SAAA;;;;;IAX5ClE,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAgC,UAAA,IAAAwC,wDAAA,uBAgBW;IACbxE,EAAA,CAAAG,YAAA,EAAM;;;;IAjBwBH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAI,UAAA,YAAAqE,OAAA,CAAAC,MAAA,CAAW;;;;;IAtF3C1E,EAAA,CAAAC,cAAA,cAAiG;IAG/FD,EAAA,CAAAgC,UAAA,IAAA2C,6CAAA,mBAqDM;IAGN3E,EAAA,CAAAgC,UAAA,IAAA4C,6CAAA,kBAuBM;IAGN5E,EAAA,CAAAgC,UAAA,IAAA6C,6CAAA,kBAkBM;IACR7E,EAAA,CAAAG,YAAA,EAAM;;;;IArGEH,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAI,UAAA,SAAA0E,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAxB,UAAA,CAAAC,MAAA,KAA8C;IAwD9CvD,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAI,UAAA,UAAA0E,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAf,YAAA,CAAAR,MAAA,KAAiD;IA0BjDvD,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAA0E,MAAA,CAAAJ,MAAA,CAAAnB,MAAA,KAAuB;;;;;;IAsB/BvD,EAAA,CAAAC,cAAA,cAAgG;IACjED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAAwE;IAA9BD,EAAA,CAAAoB,UAAA,mBAAA4D,gEAAA;MAAAhF,EAAA,CAAAuB,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAqD,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrEnF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADvNnB,MAuBaiF,uBAAuB;EAoClCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,aAAqC,EACrCC,aAAmC,EACnCC,GAAsB;IALtB,KAAAL,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IAzCL,KAAAC,QAAQ,GAAG,IAAIhG,OAAO,EAAQ;IAItC,KAAAiG,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,kBAAkB,GAAG,IAAIvG,WAAW,CAAC,EAAE,CAAC;IACxC,KAAAwG,SAAS,GAAG,IAAIxG,WAAW,EAAE;IAC7B,KAAAyG,OAAO,GAAG,IAAIzG,WAAW,EAAE;IAC3B,KAAA0G,WAAW,GAAG,IAAI1G,WAAW,CAAC,EAAE,CAAC;IACjC,KAAA2G,YAAY,GAAG,IAAI3G,WAAW,EAAE;IAChC,KAAA4G,iBAAiB,GAAG,EAAE;IAEtB;IACA,KAAAtC,YAAY,GAAkB,EAAE;IAChC,KAAAW,MAAM,GAAiB,EAAE;IACzB,KAAA4B,SAAS,GAAG,KAAK;IACjB,KAAAC,cAAc,GAAG,KAAK;IAEtB;IACA,KAAAjD,UAAU,GAAgB,EAAE;IAC5B,KAAAF,cAAc,GAA0B,IAAI;IAC5C,KAAA2B,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAyB,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAqB,EAAE;IAcpC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACnB,WAAW,CAACoB,cAAc,EAAE;IAC7C,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAACnB,aAAa,CAACoB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACxB,aAAa,CAACyB,SAAS,CAACF,QAAQ,CAACG,IAAI,CAAC;UAC3C,IAAI,CAACC,0BAA0B,CAACJ,QAAQ,CAACG,IAAI,CAAC;SAC/C,MAAM;UACL,IAAI,CAACE,0BAA0B,EAAE;;QAEnC,IAAI,CAACd,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACZ,GAAG,CAAC2B,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACF,0BAA0B,EAAE;QACjC,IAAI,CAACd,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACZ,GAAG,CAAC2B,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,0BAA0BA,CAACI,MAAW;IAC5C;IACA,IAAI,CAAChB,cAAc,GAAGgB,MAAM,CAACC,eAAe,IAAI,EAAE;IAElD;IACA,IAAI,CAAChB,eAAe,GAAGe,MAAM,CAACE,iBAAiB,IAAI,EAAE;IAErD;IACA,MAAMC,QAAQ,GAAGH,MAAM,CAACI,SAAS,IAAI,EAAE;IACvC,MAAMC,oBAAoB,GAAGF,QAAQ,CAACG,uBAAuB,IAAI,EAAE;IAEnE;IACA,IAAI,CAACzB,iBAAiB,GAAGsB,QAAQ,CAACI,sBAAsB,IAAI,UAAU;IACtE,IAAI,CAAC3B,YAAY,CAAC4B,QAAQ,CAACL,QAAQ,CAACM,iBAAiB,IAAI,cAAc,CAAC;IACxE,IAAI,CAAChC,SAAS,CAAC+B,QAAQ,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAGN,oBAAoB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1F,IAAI,CAAC3B,OAAO,CAAC8B,QAAQ,CAAC,IAAIE,IAAI,EAAE,CAAC;IAEjC;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACjD,iBAAiB,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEQkC,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACb,cAAc,GAAG,CACpB;MAAElG,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAoB,CAAE,EAClD;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAqB,CAAE,EACpD;MAAEH,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE;IAAiB,CAAE,CAC7C;IACD,IAAI,CAACgG,eAAe,GAAG,CACrB;MAAEnG,KAAK,EAAE,cAAc;MAAEG,KAAK,EAAE;IAAe,CAAE,EACjD;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAEH,KAAK,EAAE,aAAa;MAAEG,KAAK,EAAE;IAAc,CAAE,CAChD;IACD,IAAI,CAAC4F,iBAAiB,GAAG,UAAU;IACnC,IAAI,CAACD,YAAY,CAAC4B,QAAQ,CAAC,cAAc,CAAC;IAC1C,IAAI,CAAC/B,SAAS,CAAC+B,QAAQ,CAAC,IAAIE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACxE,IAAI,CAACjC,OAAO,CAAC8B,QAAQ,CAAC,IAAIE,IAAI,EAAE,CAAC;IAEjC;IACAE,UAAU,CAAC,MAAK;MACd,IAAI,CAACjD,iBAAiB,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAkD,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB;IACA;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5C,QAAQ,CAACmB,IAAI,EAAE;IACpB,IAAI,CAACnB,QAAQ,CAAC6C,QAAQ,EAAE;EAC1B;EAEQH,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACtC,kBAAkB,CAAC0C,YAAY,CACjCC,IAAI,CACH7I,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAAC+F,QAAQ,CAAC,CACzB,CACAkB,SAAS,CAAExG,KAAoB,IAAI;MAClC,IAAI,CAACsI,cAAc,CAACtI,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQiI,YAAYA,CAAA;IAClB,IAAI,CAAC/C,gBAAgB,CAACqD,sBAAsB,CACzCF,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC+F,QAAQ,CAAC,CAAC,CAC9BkB,SAAS,CAACK,IAAI,IAAG;MAChB,IAAI,CAACtB,QAAQ,GAAGsB,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACrB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C,IAAI,IAAI,CAACA,QAAQ,CAACtC,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACwC,gBAAgB,GAAG,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAClF,eAAe;;IAE5D,CAAC,CAAC;EACN;EAEQiI,cAAcA,CAACE,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAAChD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAMkD,oBAAoB,GAAGD,UAAU,CAACE,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAACnD,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACqD,MAAM,CAACC,MAAM,IACjDA,MAAM,CAACvI,UAAU,CAACoI,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACG,QAAQ,CAACL,oBAAoB,CAAC,CAClF;;EAEL;EAEA5D,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACc,SAAS,CAAC3F,KAAK,IAAI,CAAC,IAAI,CAAC4F,OAAO,CAAC5F,KAAK,IAAI,CAAC,IAAI,CAAC+F,iBAAiB,EAAE;MAC3E;;IAGF,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,MAAM+C,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvD,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACsD,UAAU,CAAC,IAAI,CAACtD,SAAS,CAAC3F,KAAK,CAAC;MAChD4F,OAAO,EAAE,IAAI,CAACqD,UAAU,CAAC,IAAI,CAACrD,OAAO,CAAC5F,KAAK,CAAC;MAC5CkJ,QAAQ,EAAE,IAAI,CAACpD,YAAY,CAAC9F,KAAK,IAAI;KACtC;IAED,MAAMmJ,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAChD,IAAI,CAACiD,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,IAAI;MACxBC,cAAc,EAAE,IAAI,CAACzD;KACtB;IAED,IAAI,CAACf,qBAAqB,CAACyE,qBAAqB,CAACN,OAAO,CAAC,CACtDd,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC+F,QAAQ,CAAC,CAAC,CAC9BkB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC+C,oBAAoB,CAAChD,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL,IAAI,CAAC8C,kBAAkB,EAAE;;QAE3B,IAAI,CAAC3D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAAC2B,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC0C,kBAAkB,EAAE;QACzB,IAAI,CAAC3D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAAC2B,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQ2C,kBAAkBA,CAAA;IACxB,IAAI,CAAClG,YAAY,GAAG,EAAE;IACtB,IAAI,CAACW,MAAM,GAAG,EAAE;IAChB,IAAI,CAACpB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACF,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC2B,eAAe,GAAG,KAAK;EAC9B;EAEQiF,oBAAoBA,CAAC7C,IAAS;IACpC;IACA,IAAI,CAACpD,YAAY,GAAGoD,IAAI,CAAC+C,aAAa,EAAEC,GAAG,CAAEC,IAAS,KAAM;MAC1DjJ,IAAI,EAAEiJ,IAAI,CAACjJ,IAAI,IAAI,IAAI,CAACsE,aAAa,CAAC4E,kBAAkB,CAACD,IAAI,CAACE,SAAS,CAAC;MACxEhK,KAAK,EAAE8J,IAAI,CAAC9J,KAAK;MACjBG,KAAK,EAAE2J,IAAI,CAAC3J,KAAK;MACjBQ,KAAK,EAAE,IAAI,CAACwE,aAAa,CAAC8E,mBAAmB,CAACH,IAAI,CAACE,SAAS,CAAC;MAC7DA,SAAS,EAAEF,IAAI,CAACE;KACjB,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAInD,IAAI,CAACqD,WAAW,IAAIrD,IAAI,CAACqD,WAAW,CAACC,gBAAgB,EAAE;MACzD,IAAI,CAACrH,cAAc,GAAG+D,IAAI,CAACqD,WAAW;MACtC,IAAI,CAAClH,UAAU,GAAG6D,IAAI,CAACqD,WAAW,CAACE,MAAM,EAAEP,GAAG,CAAEQ,KAAU,KAAM;QAC9D,GAAGA,KAAK;QACRrI,KAAK,EAAEqI,KAAK,CAACrI,KAAK,EAAE6H,GAAG,CAAEC,IAAS,KAAM;UACtCjJ,IAAI,EAAEiJ,IAAI,CAACjJ,IAAI,IAAI,IAAI,CAACsE,aAAa,CAAC4E,kBAAkB,CAACD,IAAI,CAACE,SAAS,CAAC;UACxEhK,KAAK,EAAE8J,IAAI,CAAC9J,KAAK;UACjBG,KAAK,EAAE2J,IAAI,CAAC3J,KAAK;UACjBQ,KAAK,EAAE,IAAI,CAACwE,aAAa,CAAC8E,mBAAmB,CAACH,IAAI,CAACE,SAAS,CAAC;UAC7DA,SAAS,EAAEF,IAAI,CAACE;SACjB,CAAC,CAAC,IAAI;OACR,CAAC,CAAC,IAAI,EAAE;MACT,IAAI,CAACvF,eAAe,GAAG,IAAI,CAACzB,UAAU,CAACC,MAAM,GAAG,CAAC;KAClD,MAAM;MACL,IAAI,CAACD,UAAU,GAAG,EAAE;MACpB,IAAI,CAACF,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAC2B,eAAe,GAAG,KAAK;;IAG9B;IACA,IAAI,CAACL,MAAM,GAAGyC,IAAI,CAACzC,MAAM,EAAEyF,GAAG,CAAES,KAAU,IACxC,IAAI,CAAClF,aAAa,CAACmF,YAAY,CAACD,KAAK,CAAC,CACvC,IAAI,EAAE;EACT;EAEQrB,UAAUA,CAACuB,IAAU;IAC3B,OAAOA,IAAI,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC9F,iBAAiB,EAAE;EAC1B;EAEA+F,YAAYA,CAAA;IACV,IAAI,CAAC/F,iBAAiB,EAAE;EAC1B;EAEAgG,iBAAiBA,CAAA;IACf,IAAI,CAAChG,iBAAiB,EAAE;EAC1B;EAEAiG,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAAClF,WAAW,CAAC7F,KAAK,EAAEgL,IAAI,EAAE;IAC5C,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAAC;KACvC,MAAM;MACL,IAAI,CAAClG,iBAAiB,EAAE;;EAE5B;EAEQoG,0BAA0BA,CAACF,KAAa;IAC9C,IAAI,CAAC,IAAI,CAACpF,SAAS,CAAC3F,KAAK,IAAI,CAAC,IAAI,CAAC4F,OAAO,CAAC5F,KAAK,IAAI,CAAC,IAAI,CAAC+F,iBAAiB,EAAE;MAC3E;;IAGF,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,MAAM+C,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACvD,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB,CAAC,GAAG,EAAE;MAC/DE,SAAS,EAAE,IAAI,CAACsD,UAAU,CAAC,IAAI,CAACtD,SAAS,CAAC3F,KAAK,CAAC;MAChD4F,OAAO,EAAE,IAAI,CAACqD,UAAU,CAAC,IAAI,CAACrD,OAAO,CAAC5F,KAAK,CAAC;MAC5CkJ,QAAQ,EAAE,IAAI,CAACpD,YAAY,CAAC9F,KAAK,IAAI;KACtC;IAED,MAAMmJ,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAChD,IAAI,CAACiD,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAEyB,KAAK;MACjBxB,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,IAAI,CAACzD;KACtB;IAED,IAAI,CAACf,qBAAqB,CAACyE,qBAAqB,CAACN,OAAO,CAAC,CACtDd,IAAI,CAAC9I,SAAS,CAAC,IAAI,CAAC+F,QAAQ,CAAC,CAAC,CAC9BkB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC+C,oBAAoB,CAAChD,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL,IAAI,CAAC8C,kBAAkB,EAAE;;QAE3B,IAAI,CAAC3D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAAC2B,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC0C,kBAAkB,EAAE;QACzB,IAAI,CAAC3D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACX,GAAG,CAAC2B,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEA;EACAhD,YAAYA,CAACsG,KAAiB;IAC5B,OAAOA,KAAK,CAACzD,IAAI;EACnB;EAEA9C,YAAYA,CAACuG,KAAiB;IAC5B,OAAO,IAAI,CAAClF,aAAa,CAACrB,YAAY,CAACuG,KAAK,CAACxG,IAAI,CAAC;EACpD;EAEAG,eAAeA,CAACqG,KAAiB;IAC/B,OAAOA,KAAK,CAACY,OAAO,IAAI,IAAI,CAAC/F,aAAa,CAACgG,sBAAsB,EAAE;EACrE;EAEAxH,gBAAgBA,CAAC2G,KAAiB;IAChC,OAAO,IAAI,CAAClF,aAAa,CAACzB,gBAAgB,CAAC2G,KAAK,CAAC;EACnD;EAEA;EACA9I,oBAAoBA,CAAC4J,OAAe;IAClC,MAAMf,KAAK,GAAG,IAAI,CAACrH,UAAU,CAACqI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7J,EAAE,KAAK2J,OAAO,CAAC;IACzD,IAAIf,KAAK,EAAE;MACTA,KAAK,CAACtI,WAAW,GAAG,CAACsI,KAAK,CAACtI,WAAW;;EAE1C;EAEAK,eAAeA,CAAA;IACb,IAAI,CAACY,UAAU,CAACuI,OAAO,CAAClB,KAAK,IAAG;MAC9BA,KAAK,CAACtI,WAAW,GAAG,IAAI;IAC1B,CAAC,CAAC;EACJ;EAEAQ,iBAAiBA,CAAA;IACf,IAAI,CAACS,UAAU,CAACuI,OAAO,CAAClB,KAAK,IAAG;MAC9BA,KAAK,CAACtI,WAAW,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEAW,iBAAiBA,CAAA;IACf,IAAI,CAAC+B,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;;;uBApWWK,uBAAuB,EAAApF,EAAA,CAAA8L,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAhM,EAAA,CAAA8L,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlM,EAAA,CAAA8L,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAApM,EAAA,CAAA8L,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAAtM,EAAA,CAAA8L,iBAAA,CAAAS,EAAA,CAAAC,oBAAA,GAAAxM,EAAA,CAAA8L,iBAAA,CAAA9L,EAAA,CAAAyM,iBAAA;IAAA;EAAA;;;YAAvBrH,uBAAuB;MAAAsH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA5M,EAAA,CAAA6M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxDpCnN,EAAA,CAAAC,cAAA,aAAuC;UAQlBD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,oBAA+G;UAAnGD,EAAA,CAAAoB,UAAA,yBAAAiM,mEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA/G,iBAAA,GAAAiH,MAAA;UAAA,EAA6B,6BAAAC,uEAAA;YAAA,OAAoBH,GAAA,CAAAjC,iBAAA,EAAmB;UAAA,EAAvC;UACvCnL,EAAA,CAAAgC,UAAA,IAAAwL,6CAAA,wBAAoE;UACpExN,EAAA,CAAAgC,UAAA,IAAAyL,6CAAA,wBAEa;UACfzN,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,cAA6B;UAEfD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,uBACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIrCH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,sBAAgF;UAApED,EAAA,CAAAoB,UAAA,yBAAAsM,oEAAAJ,MAAA;YAAA,OAAAF,GAAA,CAAArH,gBAAA,GAAAuH,MAAA;UAAA,EAA4B,6BAAAK,wEAAA;YAAA,OAAoBP,GAAA,CAAAnC,gBAAA,EAAkB;UAAA,EAAtC;UACtCjL,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAc,SAAA,iCAIwB;UAC1Bd,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAgC,UAAA,KAAA4L,8CAAA,wBAEa;UACf5N,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,sBAAyG;UAAhED,EAAA,CAAAoB,UAAA,6BAAAyM,wEAAA;YAAA,OAAmBT,GAAA,CAAAlC,YAAA,EAAc;UAAA,EAAC;UACzElL,EAAA,CAAAgC,UAAA,KAAA8L,8CAAA,wBAAoE;UACpE9N,EAAA,CAAAgC,UAAA,KAAA+L,8CAAA,wBAEa;UACf/N,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,iBAAsG;UAA9BD,EAAA,CAAAoB,UAAA,wBAAA4M,8DAAA;YAAA,OAAcZ,GAAA,CAAAlC,YAAA,EAAc;UAAA,EAAC;UAArGlL,EAAA,CAAAG,YAAA,EAAsG;UACtGH,EAAA,CAAAc,SAAA,iCAA6E;UAE/Ed,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAC,cAAA,iBAAkG;UAA9BD,EAAA,CAAAoB,UAAA,wBAAA6M,8DAAA;YAAA,OAAcb,GAAA,CAAAlC,YAAA,EAAc;UAAA,EAAC;UAAjGlL,EAAA,CAAAG,YAAA,EAAkG;UAClGH,EAAA,CAAAc,SAAA,iCAA2E;UAE7Ed,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA4B;UAC2BD,EAAA,CAAAoB,UAAA,mBAAA8M,0DAAA;YAAA,OAASd,GAAA,CAAAjI,iBAAA,EAAmB;UAAA,EAAC;UAChFnF,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA2B;UAIYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG1DH,EAAA,CAAAC,cAAA,eAA8B;UAKnBD,EAAA,CAAAoB,UAAA,yBAAA+M,+DAAA;YAAA,OAAef,GAAA,CAAAhC,aAAA,EAAe;UAAA,EAAC;UAHtCpL,EAAA,CAAAG,YAAA,EAGyC;UACzCH,EAAA,CAAAC,cAAA,oBAAkE;UAA1BD,EAAA,CAAAoB,UAAA,mBAAAgN,4DAAA;YAAA,OAAShB,GAAA,CAAAhC,aAAA,EAAe;UAAA,EAAC;UAACpL,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAMzFH,EAAA,CAAAC,cAAA,eAAoC;UAElCD,EAAA,CAAAgC,UAAA,KAAAqM,uCAAA,kBAGM;UAGNrO,EAAA,CAAAgC,UAAA,KAAAsM,uCAAA,kBAwGM;UAGNtO,EAAA,CAAAgC,UAAA,KAAAuM,uCAAA,mBAQM;UACRvO,EAAA,CAAAG,YAAA,EAAM;;;;;UAjPUH,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,UAAAgN,GAAA,CAAA/G,iBAAA,CAA6B,cAAA+G,GAAA,CAAA7G,cAAA;UAC1BvG,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,UAAAgN,GAAA,CAAA7G,cAAA,CAAqB;UACIvG,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAgN,GAAA,CAAA5G,cAAA,CAAiB;UAsB3CxG,EAAA,CAAAO,SAAA,IAA4B;UAA5BP,EAAA,CAAAI,UAAA,UAAAgN,GAAA,CAAArH,gBAAA,CAA4B;UAGlC/F,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,gBAAAgN,GAAA,CAAApH,kBAAA,CAAkC;UAKPhG,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAgN,GAAA,CAAAtH,gBAAA,CAAmB;UAexC9F,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,gBAAAgN,GAAA,CAAAhH,YAAA,CAA4B,cAAAgH,GAAA,CAAA7G,cAAA;UACzBvG,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,UAAAgN,GAAA,CAAA7G,cAAA,CAAqB;UACKvG,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAAgN,GAAA,CAAA3G,eAAA,CAAkB;UAe3CzG,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,kBAAAoO,GAAA,CAA6B,gBAAApB,GAAA,CAAAnH,SAAA;UACZjG,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,QAAAoO,GAAA,CAAmB;UAapCxO,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,kBAAAqO,GAAA,CAA2B,gBAAArB,GAAA,CAAAlH,OAAA;UACVlG,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,QAAAqO,GAAA,CAAiB;UA8B3CzO,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,gBAAAgN,GAAA,CAAAjH,WAAA,CAA2B;UAUhCnG,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAgN,GAAA,CAAA9G,SAAA,CAAe;UAMftG,EAAA,CAAAO,SAAA,GAAkE;UAAlEP,EAAA,CAAAI,UAAA,UAAAgN,GAAA,CAAA9G,SAAA,KAAA8G,GAAA,CAAArJ,YAAA,CAAAR,MAAA,QAAA6J,GAAA,CAAA1I,MAAA,CAAAnB,MAAA,MAAkE;UA2GlEvD,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAI,UAAA,UAAAgN,GAAA,CAAA9G,SAAA,IAAA8G,GAAA,CAAArJ,YAAA,CAAAR,MAAA,UAAA6J,GAAA,CAAA1I,MAAA,CAAAnB,MAAA,OAAoE;;;qBD5M9E3E,YAAY,EAAA8P,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZhQ,aAAa,EAAAiQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbpQ,eAAe,EAAAqQ,EAAA,CAAAC,SAAA,EACfrQ,aAAa,EAAAsQ,EAAA,CAAAC,OAAA,EACbtQ,eAAe,EAAAuQ,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EAAAC,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACf7Q,kBAAkB,EAClBC,cAAc,EAAA6Q,GAAA,CAAAC,QAAA,EACd7Q,mBAAmB,EAAA8Q,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnBhR,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EAAA+Q,GAAA,CAAAC,kBAAA,EACxB/Q,cAAc,EAAAgR,GAAA,CAAAC,kBAAA,EACdhR,wBAAwB,EAAAiR,GAAA,CAAAC,wBAAA,EACxBhR,mBAAmB,EAAAiR,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,oBAAA,EACnBnR,WAAW;MAAAoR,MAAA;IAAA;EAAA;;SAKF3L,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}